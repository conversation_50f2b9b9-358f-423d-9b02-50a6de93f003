/**
 * 任务编排器
 * 负责智能任务分解、Agent选择和执行协调
 */

import { EventEmitter } from "events"
import { 
	Agent, 
	AgentTask, 
	AgentResult, 
	ExecutionContext, 
	TaskPriority, 
	TaskStatus,
	AgentCapability,
	AgentMessage,
	AgentMessageType
} from "../agents/types"
import { AgentRegistry } from "../agents/AgentRegistry"

/**
 * 执行计划
 */
export interface ExecutionPlan {
	id: string
	description: string
	tasks: AgentTask[]
	dependencies: Map<string, string[]> // taskId -> dependentTaskIds
	estimatedDuration: number
	priority: TaskPriority
	createdAt: Date
}

/**
 * 执行状态
 */
export interface ExecutionStatus {
	planId: string
	status: "pending" | "running" | "completed" | "failed" | "cancelled"
	progress: number // 0-100
	completedTasks: number
	totalTasks: number
	currentTasks: Map<string, string> // taskId -> agentId
	results: Map<string, AgentResult>
	errors: string[]
	startedAt?: Date
	completedAt?: Date
}

/**
 * 任务分解策略
 */
export enum TaskDecompositionStrategy {
	SEQUENTIAL = "sequential",
	PARALLEL = "parallel",
	HYBRID = "hybrid",
	CAPABILITY_BASED = "capability_based"
}

/**
 * 任务编排器事件
 */
export interface TaskOrchestratorEvents {
	planCreated: (plan: ExecutionPlan) => void
	executionStarted: (planId: string) => void
	taskAssigned: (taskId: string, agentId: string) => void
	taskCompleted: (taskId: string, result: AgentResult) => void
	taskFailed: (taskId: string, error: string) => void
	executionCompleted: (planId: string, status: ExecutionStatus) => void
	executionFailed: (planId: string, error: string) => void
}

/**
 * 任务编排器
 * 管理复杂任务的分解、分发和执行协调
 */
export class TaskOrchestrator extends EventEmitter {
	private agentRegistry: AgentRegistry
	private executionPlans: Map<string, ExecutionPlan> = new Map()
	private executionStatuses: Map<string, ExecutionStatus> = new Map()
	private decompositionStrategy: TaskDecompositionStrategy = TaskDecompositionStrategy.HYBRID

	constructor(agentRegistry: AgentRegistry) {
		super()
		this.agentRegistry = agentRegistry
	}

	/**
	 * 分解用户请求为执行计划
	 */
	public async decomposeUserRequest(
		userRequest: string, 
		context: ExecutionContext,
		strategy?: TaskDecompositionStrategy
	): Promise<ExecutionPlan> {
		const planId = `plan_${Date.now()}`
		const currentStrategy = strategy || this.decompositionStrategy

		// 分析用户请求，识别意图和需求
		const intent = await this.analyzeUserIntent(userRequest, context)
		
		// 根据意图生成任务列表
		const tasks = await this.generateTasks(intent, context, currentStrategy)
		
		// 分析任务依赖关系
		const dependencies = this.analyzeDependencies(tasks)
		
		// 估算执行时间
		const estimatedDuration = this.estimateExecutionTime(tasks, dependencies)

		const plan: ExecutionPlan = {
			id: planId,
			description: userRequest,
			tasks,
			dependencies,
			estimatedDuration,
			priority: this.determinePriority(intent),
			createdAt: new Date()
		}

		this.executionPlans.set(planId, plan)
		this.emit("planCreated", plan)

		return plan
	}

	/**
	 * 执行计划
	 */
	public async executePlan(planId: string, context: ExecutionContext): Promise<ExecutionStatus> {
		const plan = this.executionPlans.get(planId)
		if (!plan) {
			throw new Error(`Execution plan ${planId} not found`)
		}

		// 初始化执行状态
		const status: ExecutionStatus = {
			planId,
			status: "running",
			progress: 0,
			completedTasks: 0,
			totalTasks: plan.tasks.length,
			currentTasks: new Map(),
			results: new Map(),
			errors: [],
			startedAt: new Date()
		}

		this.executionStatuses.set(planId, status)
		this.emit("executionStarted", planId)

		try {
			// 执行任务
			await this.executeTasksInPlan(plan, status, context)
			
			// 更新最终状态
			status.status = "completed"
			status.progress = 100
			status.completedAt = new Date()
			
			this.emit("executionCompleted", planId, status)
			
		} catch (error) {
			status.status = "failed"
			status.errors.push(error instanceof Error ? error.message : String(error))
			this.emit("executionFailed", planId, error instanceof Error ? error.message : String(error))
		}

		return status
	}

	/**
	 * 分析用户意图
	 */
	private async analyzeUserIntent(userRequest: string, context: ExecutionContext): Promise<any> {
		// 简化的意图分析，实际实现可以使用NLP或规则引擎
		const intent = {
			type: "unknown",
			entities: [],
			capabilities: [] as AgentCapability[],
			complexity: "medium",
			urgency: "normal"
		}

		// 关键词匹配来识别意图
		const request = userRequest.toLowerCase()

		if (request.includes("create") || request.includes("generate") || request.includes("write")) {
			intent.type = "code_generation"
			intent.capabilities.push("code-writing", "file-creation")
		}

		if (request.includes("review") || request.includes("check") || request.includes("analyze")) {
			intent.type = "code_review"
			intent.capabilities.push("code-analysis", "bug-detection")
		}

		if (request.includes("test") || request.includes("testing")) {
			intent.type = "testing"
			intent.capabilities.push("test-generation", "test-execution")
		}

		if (request.includes("fix") || request.includes("debug")) {
			intent.type = "debugging"
			intent.capabilities.push("debugging", "bug-detection")
		}

		if (request.includes("refactor") || request.includes("improve")) {
			intent.type = "refactoring"
			intent.capabilities.push("refactoring", "code-analysis")
		}

		// 复杂度分析
		if (request.includes("complex") || request.includes("large") || request.includes("entire")) {
			intent.complexity = "high"
		} else if (request.includes("simple") || request.includes("small") || request.includes("quick")) {
			intent.complexity = "low"
		}

		// 紧急程度分析
		if (request.includes("urgent") || request.includes("asap") || request.includes("immediately")) {
			intent.urgency = "high"
		}

		return intent
	}

	/**
	 * 生成任务列表
	 */
	private async generateTasks(
		intent: any, 
		context: ExecutionContext, 
		strategy: TaskDecompositionStrategy
	): Promise<AgentTask[]> {
		const tasks: AgentTask[] = []

		switch (intent.type) {
			case "code_generation":
				tasks.push(...this.generateCodeGenerationTasks(intent, context))
				break
			
			case "code_review":
				tasks.push(...this.generateCodeReviewTasks(intent, context))
				break
			
			case "testing":
				tasks.push(...this.generateTestingTasks(intent, context))
				break
			
			case "debugging":
				tasks.push(...this.generateDebuggingTasks(intent, context))
				break
			
			case "refactoring":
				tasks.push(...this.generateRefactoringTasks(intent, context))
				break
			
			default:
				// 通用任务分解
				tasks.push(...this.generateGenericTasks(intent, context))
		}

		// 根据策略调整任务结构
		return this.applyDecompositionStrategy(tasks, strategy)
	}

	/**
	 * 生成代码生成任务
	 */
	private generateCodeGenerationTasks(intent: any, context: ExecutionContext): AgentTask[] {
		const tasks: AgentTask[] = []

		// 主要代码生成任务
		tasks.push({
			id: `task_${Date.now()}_1`,
			type: "create_file",
			description: "Generate main code file",
			priority: TaskPriority.HIGH,
			status: TaskStatus.PENDING,
			requiredCapabilities: ["code-writing", "file-creation"],
			parameters: intent.parameters || {},
			createdAt: new Date(),
			updatedAt: new Date()
		})

		// 如果复杂度高，添加审查任务
		if (intent.complexity === "high") {
			tasks.push({
				id: `task_${Date.now()}_2`,
				type: "review_changes",
				description: "Review generated code",
				priority: TaskPriority.MEDIUM,
				status: TaskStatus.PENDING,
				requiredCapabilities: ["code-analysis"],
				parameters: {},
				dependencies: [tasks[0].id],
				createdAt: new Date(),
				updatedAt: new Date()
			})

			// 添加测试生成任务
			tasks.push({
				id: `task_${Date.now()}_3`,
				type: "generate_tests",
				description: "Generate tests for new code",
				priority: TaskPriority.MEDIUM,
				status: TaskStatus.PENDING,
				requiredCapabilities: ["test-generation"],
				parameters: {},
				dependencies: [tasks[0].id],
				createdAt: new Date(),
				updatedAt: new Date()
			})
		}

		return tasks
	}

	/**
	 * 生成代码审查任务
	 */
	private generateCodeReviewTasks(intent: any, context: ExecutionContext): AgentTask[] {
		return [{
			id: `task_${Date.now()}`,
			type: "analyze_code",
			description: "Perform code analysis and review",
			priority: TaskPriority.HIGH,
			status: TaskStatus.PENDING,
			requiredCapabilities: ["code-analysis", "bug-detection"],
			parameters: intent.parameters || {},
			createdAt: new Date(),
			updatedAt: new Date()
		}]
	}

	/**
	 * 生成测试任务
	 */
	private generateTestingTasks(intent: any, context: ExecutionContext): AgentTask[] {
		const tasks: AgentTask[] = []

		// 测试生成
		tasks.push({
			id: `task_${Date.now()}_1`,
			type: "generate_tests",
			description: "Generate test cases",
			priority: TaskPriority.HIGH,
			status: TaskStatus.PENDING,
			requiredCapabilities: ["test-generation"],
			parameters: intent.parameters || {},
			createdAt: new Date(),
			updatedAt: new Date()
		})

		// 测试执行
		tasks.push({
			id: `task_${Date.now()}_2`,
			type: "run_tests",
			description: "Execute test cases",
			priority: TaskPriority.HIGH,
			status: TaskStatus.PENDING,
			requiredCapabilities: ["test-execution"],
			parameters: {},
			dependencies: [tasks[0].id],
			createdAt: new Date(),
			updatedAt: new Date()
		})

		return tasks
	}

	/**
	 * 生成调试任务
	 */
	private generateDebuggingTasks(intent: any, context: ExecutionContext): AgentTask[] {
		return [{
			id: `task_${Date.now()}`,
			type: "debug_issue",
			description: "Debug and fix issues",
			priority: TaskPriority.HIGH,
			status: TaskStatus.PENDING,
			requiredCapabilities: ["debugging", "bug-detection"],
			parameters: intent.parameters || {},
			createdAt: new Date(),
			updatedAt: new Date()
		}]
	}

	/**
	 * 生成重构任务
	 */
	private generateRefactoringTasks(intent: any, context: ExecutionContext): AgentTask[] {
		const tasks: AgentTask[] = []

		// 代码分析
		tasks.push({
			id: `task_${Date.now()}_1`,
			type: "analyze_code",
			description: "Analyze code for refactoring opportunities",
			priority: TaskPriority.MEDIUM,
			status: TaskStatus.PENDING,
			requiredCapabilities: ["code-analysis"],
			parameters: intent.parameters || {},
			createdAt: new Date(),
			updatedAt: new Date()
		})

		// 重构执行
		tasks.push({
			id: `task_${Date.now()}_2`,
			type: "refactor_code",
			description: "Perform code refactoring",
			priority: TaskPriority.HIGH,
			status: TaskStatus.PENDING,
			requiredCapabilities: ["refactoring"],
			parameters: {},
			dependencies: [tasks[0].id],
			createdAt: new Date(),
			updatedAt: new Date()
		})

		return tasks
	}

	/**
	 * 生成通用任务
	 */
	private generateGenericTasks(intent: any, context: ExecutionContext): AgentTask[] {
		return [{
			id: `task_${Date.now()}`,
			type: "general_assistance",
			description: "Provide general assistance",
			priority: TaskPriority.MEDIUM,
			status: TaskStatus.PENDING,
			requiredCapabilities: intent.capabilities || [],
			parameters: intent.parameters || {},
			createdAt: new Date(),
			updatedAt: new Date()
		}]
	}

	/**
	 * 应用分解策略
	 */
	private applyDecompositionStrategy(tasks: AgentTask[], strategy: TaskDecompositionStrategy): AgentTask[] {
		switch (strategy) {
			case TaskDecompositionStrategy.SEQUENTIAL:
				return this.makeTasksSequential(tasks)
			
			case TaskDecompositionStrategy.PARALLEL:
				return this.makeTasksParallel(tasks)
			
			case TaskDecompositionStrategy.HYBRID:
				return this.optimizeTaskOrder(tasks)
			
			default:
				return tasks
		}
	}

	/**
	 * 使任务顺序执行
	 */
	private makeTasksSequential(tasks: AgentTask[]): AgentTask[] {
		for (let i = 1; i < tasks.length; i++) {
			tasks[i].dependencies = [tasks[i - 1].id]
		}
		return tasks
	}

	/**
	 * 使任务并行执行
	 */
	private makeTasksParallel(tasks: AgentTask[]): AgentTask[] {
		// 移除所有依赖关系（除了明确需要的）
		tasks.forEach(task => {
			if (!task.dependencies || task.dependencies.length === 0) {
				delete task.dependencies
			}
		})
		return tasks
	}

	/**
	 * 优化任务顺序
	 */
	private optimizeTaskOrder(tasks: AgentTask[]): AgentTask[] {
		// 智能分析哪些任务可以并行，哪些必须顺序执行
		// 这里是简化实现，实际可以更复杂
		return tasks
	}

	/**
	 * 分析任务依赖关系
	 */
	private analyzeDependencies(tasks: AgentTask[]): Map<string, string[]> {
		const dependencies = new Map<string, string[]>()
		
		tasks.forEach(task => {
			if (task.dependencies && task.dependencies.length > 0) {
				dependencies.set(task.id, task.dependencies)
			}
		})
		
		return dependencies
	}

	/**
	 * 估算执行时间
	 */
	private estimateExecutionTime(tasks: AgentTask[], dependencies: Map<string, string[]>): number {
		// 简化的时间估算，实际可以基于历史数据和任务复杂度
		const baseTime = 10 // 每个任务基础时间（分钟）
		const parallelTasks = tasks.filter(task => !dependencies.has(task.id))
		const sequentialTasks = tasks.filter(task => dependencies.has(task.id))
		
		// 并行任务取最大时间，顺序任务累加时间
		const parallelTime = parallelTasks.length > 0 ? baseTime : 0
		const sequentialTime = sequentialTasks.length * baseTime
		
		return parallelTime + sequentialTime
	}

	/**
	 * 确定优先级
	 */
	private determinePriority(intent: any): TaskPriority {
		if (intent.urgency === "high") {
			return TaskPriority.HIGH
		} else if (intent.complexity === "high") {
			return TaskPriority.MEDIUM
		} else {
			return TaskPriority.LOW
		}
	}

	/**
	 * 执行计划中的任务
	 */
	private async executeTasksInPlan(
		plan: ExecutionPlan, 
		status: ExecutionStatus, 
		context: ExecutionContext
	): Promise<void> {
		const readyTasks = this.getReadyTasks(plan, status)
		
		while (readyTasks.length > 0 || status.currentTasks.size > 0) {
			// 启动准备好的任务
			for (const task of readyTasks) {
				await this.assignAndExecuteTask(task, status, context)
			}
			
			// 等待任务完成
			await this.waitForTaskCompletion(status)
			
			// 更新进度
			this.updateProgress(status)
			
			// 获取下一批准备好的任务
			readyTasks.splice(0, readyTasks.length, ...this.getReadyTasks(plan, status))
		}
	}

	/**
	 * 获取准备好执行的任务
	 */
	private getReadyTasks(plan: ExecutionPlan, status: ExecutionStatus): AgentTask[] {
		return plan.tasks.filter(task => {
			// 任务未完成且未在执行中
			if (task.status !== TaskStatus.PENDING || status.currentTasks.has(task.id)) {
				return false
			}
			
			// 检查依赖是否已完成
			const dependencies = plan.dependencies.get(task.id) || []
			return dependencies.every(depId => {
				const depTask = plan.tasks.find(t => t.id === depId)
				return depTask?.status === TaskStatus.COMPLETED
			})
		})
	}

	/**
	 * 分配并执行任务
	 */
	private async assignAndExecuteTask(
		task: AgentTask, 
		status: ExecutionStatus, 
		context: ExecutionContext
	): Promise<void> {
		// 选择合适的Agent
		const agent = this.agentRegistry.selectAgentForTask(task)
		if (!agent) {
			throw new Error(`No suitable agent found for task ${task.id}`)
		}

		// 记录任务分配
		status.currentTasks.set(task.id, agent.id)
		this.emit("taskAssigned", task.id, agent.id)

		// 异步执行任务
		this.executeTaskAsync(task, agent, status, context)
	}

	/**
	 * 异步执行任务
	 */
	private async executeTaskAsync(
		task: AgentTask, 
		agent: Agent, 
		status: ExecutionStatus, 
		context: ExecutionContext
	): Promise<void> {
		try {
			const result = await agent.execute(task, context)
			
			// 记录结果
			status.results.set(task.id, result)
			status.currentTasks.delete(task.id)
			
			if (result.success) {
				task.status = TaskStatus.COMPLETED
				status.completedTasks++
				this.emit("taskCompleted", task.id, result)
			} else {
				task.status = TaskStatus.FAILED
				status.errors.push(`Task ${task.id} failed: ${result.error}`)
				this.emit("taskFailed", task.id, result.error || "Unknown error")
			}
			
		} catch (error) {
			task.status = TaskStatus.FAILED
			status.currentTasks.delete(task.id)
			const errorMessage = error instanceof Error ? error.message : String(error)
			status.errors.push(`Task ${task.id} failed: ${errorMessage}`)
			this.emit("taskFailed", task.id, errorMessage)
		}
	}

	/**
	 * 等待任务完成
	 */
	private async waitForTaskCompletion(status: ExecutionStatus): Promise<void> {
		// 简化实现：等待一小段时间让任务有机会完成
		await new Promise(resolve => setTimeout(resolve, 100))
	}

	/**
	 * 更新进度
	 */
	private updateProgress(status: ExecutionStatus): void {
		status.progress = Math.round((status.completedTasks / status.totalTasks) * 100)
	}

	/**
	 * 获取执行状态
	 */
	public getExecutionStatus(planId: string): ExecutionStatus | undefined {
		return this.executionStatuses.get(planId)
	}

	/**
	 * 取消执行
	 */
	public cancelExecution(planId: string): boolean {
		const status = this.executionStatuses.get(planId)
		if (!status || status.status === "completed") {
			return false
		}

		status.status = "cancelled"
		status.completedAt = new Date()
		
		return true
	}

	/**
	 * 设置分解策略
	 */
	public setDecompositionStrategy(strategy: TaskDecompositionStrategy): void {
		this.decompositionStrategy = strategy
	}
}
