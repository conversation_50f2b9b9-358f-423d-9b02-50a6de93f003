/**
 * 共享上下文管理器
 * 管理所有Agent共享的项目上下文、对话历史和工作成果
 */

import { EventEmitter } from "events"
import { Anthropic } from "@anthropic-ai/sdk"
import { 
	ExecutionContext, 
	AgentMessage, 
	AgentResult, 
	AgentTask 
} from "../agents/types"

/**
 * 项目上下文
 */
export interface ProjectContext {
	workspaceRoot: string
	currentWorkingDirectory: string
	projectFiles: string[]
	recentFiles: string[]
	gitBranch?: string
	gitCommit?: string
	dependencies: Record<string, string>
	configuration: Record<string, any>
	lastUpdated: Date
}

/**
 * 对话上下文
 */
export interface ConversationContext {
	sessionId: string
	messages: Anthropic.Messages.MessageParam[]
	currentUserMessage?: string
	messageCount: number
	startTime: Date
	lastActivity: Date
}

/**
 * 工作成果
 */
export interface WorkArtifact {
	id: string
	type: "file" | "code" | "test" | "documentation" | "analysis"
	title: string
	description: string
	content: any
	metadata: Record<string, any>
	createdBy: string
	createdAt: Date
	updatedAt: Date
	version: number
	tags: string[]
}

/**
 * 任务历史
 */
export interface TaskHistory {
	taskId: string
	agentId: string
	startTime: Date
	endTime?: Date
	status: string
	result?: AgentResult
	duration?: number
}

/**
 * 上下文快照
 */
export interface ContextSnapshot {
	id: string
	timestamp: Date
	projectContext: ProjectContext
	conversationContext: ConversationContext
	artifacts: WorkArtifact[]
	taskHistory: TaskHistory[]
	agentStates: Record<string, any>
}

/**
 * 共享上下文管理器事件
 */
export interface SharedContextEvents {
	contextUpdated: (context: ExecutionContext) => void
	artifactCreated: (artifact: WorkArtifact) => void
	artifactUpdated: (artifact: WorkArtifact) => void
	snapshotCreated: (snapshot: ContextSnapshot) => void
	projectContextChanged: (context: ProjectContext) => void
}

/**
 * 共享上下文管理器
 * 提供统一的上下文管理和状态同步
 */
export class SharedContextManager extends EventEmitter {
	private projectContext: ProjectContext
	private conversationContext: ConversationContext
	private artifacts: Map<string, WorkArtifact> = new Map()
	private taskHistory: TaskHistory[] = []
	private agentStates: Map<string, any> = new Map()
	private snapshots: Map<string, ContextSnapshot> = new Map()
	private sharedState: Map<string, any> = new Map()

	constructor(initialContext: Partial<ExecutionContext>) {
		super()
		
		this.projectContext = {
			workspaceRoot: initialContext.workspaceRoot || "",
			currentWorkingDirectory: initialContext.currentWorkingDirectory || "",
			projectFiles: initialContext.projectFiles || [],
			recentFiles: [],
			dependencies: {},
			configuration: {},
			lastUpdated: new Date()
		}

		this.conversationContext = {
			sessionId: `session_${Date.now()}`,
			messages: initialContext.conversationHistory || [],
			currentUserMessage: initialContext.currentUserMessage,
			messageCount: 0,
			startTime: new Date(),
			lastActivity: new Date()
		}
	}

	/**
	 * 获取当前执行上下文
	 */
	public getCurrentContext(): ExecutionContext {
		return {
			workspaceRoot: this.projectContext.workspaceRoot,
			currentWorkingDirectory: this.projectContext.currentWorkingDirectory,
			projectFiles: this.projectContext.projectFiles,
			conversationHistory: this.conversationContext.messages,
			currentUserMessage: this.conversationContext.currentUserMessage,
			currentTask: this.getCurrentTask(),
			relatedTasks: this.getRelatedTasks(),
			sharedState: Object.fromEntries(this.sharedState),
			availableTools: this.getAvailableTools(),
			mcpServers: this.getMcpServers(),
			collaboratingAgents: Array.from(this.agentStates.keys()),
			messageHistory: this.getRecentMessages()
		}
	}

	/**
	 * 更新项目上下文
	 */
	public updateProjectContext(updates: Partial<ProjectContext>): void {
		this.projectContext = {
			...this.projectContext,
			...updates,
			lastUpdated: new Date()
		}
		
		this.emit("projectContextChanged", this.projectContext)
		this.emit("contextUpdated", this.getCurrentContext())
	}

	/**
	 * 更新对话上下文
	 */
	public updateConversationContext(updates: Partial<ConversationContext>): void {
		this.conversationContext = {
			...this.conversationContext,
			...updates,
			lastActivity: new Date()
		}
		
		this.emit("contextUpdated", this.getCurrentContext())
	}

	/**
	 * 添加消息到对话历史
	 */
	public addMessage(message: Anthropic.Messages.MessageParam): void {
		this.conversationContext.messages.push(message)
		this.conversationContext.messageCount++
		this.conversationContext.lastActivity = new Date()
		
		this.emit("contextUpdated", this.getCurrentContext())
	}

	/**
	 * 创建工作成果
	 */
	public createArtifact(
		type: WorkArtifact["type"],
		title: string,
		description: string,
		content: any,
		createdBy: string,
		metadata: Record<string, any> = {},
		tags: string[] = []
	): WorkArtifact {
		const artifact: WorkArtifact = {
			id: `artifact_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
			type,
			title,
			description,
			content,
			metadata,
			createdBy,
			createdAt: new Date(),
			updatedAt: new Date(),
			version: 1,
			tags
		}

		this.artifacts.set(artifact.id, artifact)
		this.emit("artifactCreated", artifact)
		
		return artifact
	}

	/**
	 * 更新工作成果
	 */
	public updateArtifact(artifactId: string, updates: Partial<WorkArtifact>): WorkArtifact | null {
		const artifact = this.artifacts.get(artifactId)
		if (!artifact) {
			return null
		}

		const updatedArtifact = {
			...artifact,
			...updates,
			updatedAt: new Date(),
			version: artifact.version + 1
		}

		this.artifacts.set(artifactId, updatedArtifact)
		this.emit("artifactUpdated", updatedArtifact)
		
		return updatedArtifact
	}

	/**
	 * 获取工作成果
	 */
	public getArtifact(artifactId: string): WorkArtifact | null {
		return this.artifacts.get(artifactId) || null
	}

	/**
	 * 获取所有工作成果
	 */
	public getAllArtifacts(): WorkArtifact[] {
		return Array.from(this.artifacts.values())
	}

	/**
	 * 按类型获取工作成果
	 */
	public getArtifactsByType(type: WorkArtifact["type"]): WorkArtifact[] {
		return Array.from(this.artifacts.values()).filter(artifact => artifact.type === type)
	}

	/**
	 * 按标签获取工作成果
	 */
	public getArtifactsByTag(tag: string): WorkArtifact[] {
		return Array.from(this.artifacts.values()).filter(artifact => artifact.tags.includes(tag))
	}

	/**
	 * 记录任务历史
	 */
	public recordTaskStart(taskId: string, agentId: string): void {
		const history: TaskHistory = {
			taskId,
			agentId,
			startTime: new Date(),
			status: "running"
		}
		
		this.taskHistory.push(history)
	}

	/**
	 * 记录任务完成
	 */
	public recordTaskCompletion(taskId: string, result: AgentResult): void {
		const history = this.taskHistory.find(h => h.taskId === taskId && !h.endTime)
		if (history) {
			history.endTime = new Date()
			history.status = result.success ? "completed" : "failed"
			history.result = result
			history.duration = history.endTime.getTime() - history.startTime.getTime()
		}
	}

	/**
	 * 获取任务历史
	 */
	public getTaskHistory(limit?: number): TaskHistory[] {
		const sorted = this.taskHistory.sort((a, b) => b.startTime.getTime() - a.startTime.getTime())
		return limit ? sorted.slice(0, limit) : sorted
	}

	/**
	 * 更新Agent状态
	 */
	public updateAgentState(agentId: string, state: any): void {
		this.agentStates.set(agentId, {
			...this.agentStates.get(agentId),
			...state,
			lastUpdated: new Date()
		})
	}

	/**
	 * 获取Agent状态
	 */
	public getAgentState(agentId: string): any {
		return this.agentStates.get(agentId)
	}

	/**
	 * 设置共享状态
	 */
	public setSharedState(key: string, value: any): void {
		this.sharedState.set(key, value)
		this.emit("contextUpdated", this.getCurrentContext())
	}

	/**
	 * 获取共享状态
	 */
	public getSharedState(key: string): any {
		return this.sharedState.get(key)
	}

	/**
	 * 创建上下文快照
	 */
	public createSnapshot(description?: string): ContextSnapshot {
		const snapshot: ContextSnapshot = {
			id: `snapshot_${Date.now()}`,
			timestamp: new Date(),
			projectContext: { ...this.projectContext },
			conversationContext: { ...this.conversationContext },
			artifacts: Array.from(this.artifacts.values()),
			taskHistory: [...this.taskHistory],
			agentStates: Object.fromEntries(this.agentStates)
		}

		this.snapshots.set(snapshot.id, snapshot)
		this.emit("snapshotCreated", snapshot)
		
		return snapshot
	}

	/**
	 * 恢复快照
	 */
	public restoreSnapshot(snapshotId: string): boolean {
		const snapshot = this.snapshots.get(snapshotId)
		if (!snapshot) {
			return false
		}

		this.projectContext = { ...snapshot.projectContext }
		this.conversationContext = { ...snapshot.conversationContext }
		this.artifacts = new Map(snapshot.artifacts.map(a => [a.id, a]))
		this.taskHistory = [...snapshot.taskHistory]
		this.agentStates = new Map(Object.entries(snapshot.agentStates))

		this.emit("contextUpdated", this.getCurrentContext())
		return true
	}

	/**
	 * 获取快照列表
	 */
	public getSnapshots(): ContextSnapshot[] {
		return Array.from(this.snapshots.values()).sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
	}

	/**
	 * 清理旧数据
	 */
	public cleanup(retentionDays: number = 7): void {
		const cutoffTime = Date.now() - (retentionDays * 24 * 60 * 60 * 1000)

		// 清理旧的任务历史
		this.taskHistory = this.taskHistory.filter(h => h.startTime.getTime() > cutoffTime)

		// 清理旧的快照
		for (const [id, snapshot] of this.snapshots) {
			if (snapshot.timestamp.getTime() < cutoffTime) {
				this.snapshots.delete(id)
			}
		}

		// 清理旧的工作成果（可选）
		for (const [id, artifact] of this.artifacts) {
			if (artifact.createdAt.getTime() < cutoffTime && artifact.tags.includes("temporary")) {
				this.artifacts.delete(id)
			}
		}
	}

	/**
	 * 获取当前任务
	 */
	private getCurrentTask(): AgentTask | undefined {
		// 从任务历史中获取最近的运行中任务
		const runningTask = this.taskHistory.find(h => h.status === "running")
		if (runningTask) {
			// 这里需要从任务存储中获取完整的任务信息
			// 简化实现，返回undefined
		}
		return undefined
	}

	/**
	 * 获取相关任务
	 */
	private getRelatedTasks(): AgentTask[] {
		// 简化实现，返回空数组
		return []
	}

	/**
	 * 获取可用工具
	 */
	private getAvailableTools(): string[] {
		// 从项目配置或Agent注册表中获取
		return ["write_to_file", "read_file", "execute_command", "str_replace_editor"]
	}

	/**
	 * 获取MCP服务器
	 */
	private getMcpServers(): string[] {
		// 从项目配置中获取
		return []
	}

	/**
	 * 获取最近的消息
	 */
	private getRecentMessages(): AgentMessage[] {
		// 简化实现，返回空数组
		return []
	}

	/**
	 * 导出上下文数据
	 */
	public exportContext(): any {
		return {
			projectContext: this.projectContext,
			conversationContext: this.conversationContext,
			artifacts: Array.from(this.artifacts.values()),
			taskHistory: this.taskHistory,
			agentStates: Object.fromEntries(this.agentStates),
			sharedState: Object.fromEntries(this.sharedState)
		}
	}

	/**
	 * 导入上下文数据
	 */
	public importContext(data: any): void {
		if (data.projectContext) {
			this.projectContext = data.projectContext
		}
		if (data.conversationContext) {
			this.conversationContext = data.conversationContext
		}
		if (data.artifacts) {
			this.artifacts = new Map(data.artifacts.map((a: WorkArtifact) => [a.id, a]))
		}
		if (data.taskHistory) {
			this.taskHistory = data.taskHistory
		}
		if (data.agentStates) {
			this.agentStates = new Map(Object.entries(data.agentStates))
		}
		if (data.sharedState) {
			this.sharedState = new Map(Object.entries(data.sharedState))
		}

		this.emit("contextUpdated", this.getCurrentContext())
	}
}
