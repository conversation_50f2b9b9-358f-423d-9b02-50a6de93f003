/**
 * 多Agent统一对话界面
 * 提供与现有Task类兼容的接口，但内部使用多Agent协作
 */

import { EventEmitter } from "events"
import { Anthropic } from "@anthropic-ai/sdk"
import * as vscode from "vscode"

import { AgentSystem } from "../agents"
import { TaskRouter, RoutingResult } from "../routing/TaskRouter"
import { TaskOrchestrator, ExecutionStatus } from "../orchestration/TaskOrchestrator"
import { AgentCommunicationBus } from "../communication/AgentCommunicationBus"
import { SharedContextManager } from "../context/SharedContextManager"
import { 
	ExecutionContext, 
	AgentMessage, 
	AgentMessageType,
	AgentResult 
} from "../agents/types"

// 导入现有类型以保持兼容性
import { ToolResponse } from "../task"
import { ClineMessage, ClineAsk } from "@shared/ExtensionMessage"

/**
 * 多Agent对话界面配置
 */
export interface MultiAgentChatConfig {
	enableCollaboration: boolean
	maxConcurrentAgents: number
	responseTimeout: number
	autoApprovalEnabled: boolean
	debugMode: boolean
}

/**
 * 执行状态回调
 */
export interface ExecutionCallbacks {
	onProgress?: (progress: number, status: string) => void
	onAgentAssigned?: (taskId: string, agentId: string) => void
	onTaskCompleted?: (taskId: string, result: AgentResult) => void
	onError?: (error: string) => void
}

/**
 * 多Agent统一对话界面
 * 对外提供与原Task类相同的接口，内部使用多Agent协作
 */
export class MultiAgentChatInterface extends EventEmitter {
	private agentSystem: AgentSystem
	private taskRouter: TaskRouter
	private taskOrchestrator: TaskOrchestrator
	private communicationBus: AgentCommunicationBus
	private contextManager: SharedContextManager
	
	private config: MultiAgentChatConfig
	private currentExecution: ExecutionStatus | null = null
	private messageHistory: ClineMessage[] = []
	private isProcessing: boolean = false

	constructor(
		initialContext: Partial<ExecutionContext>,
		config: Partial<MultiAgentChatConfig> = {}
	) {
		super()
		
		this.config = {
			enableCollaboration: true,
			maxConcurrentAgents: 3,
			responseTimeout: 300000, // 5分钟
			autoApprovalEnabled: false,
			debugMode: false,
			...config
		}

		// 初始化多Agent系统组件
		this.agentSystem = new AgentSystem()
		this.contextManager = new SharedContextManager(initialContext)
		this.taskOrchestrator = new TaskOrchestrator(this.agentSystem.getRegistry())
		this.communicationBus = new AgentCommunicationBus(this.agentSystem.getRegistry())
		this.taskRouter = new TaskRouter(this.agentSystem.getRegistry(), this.taskOrchestrator)

		this.setupEventHandlers()
	}

	/**
	 * 初始化多Agent系统
	 */
	public async initialize(): Promise<void> {
		await this.agentSystem.initialize()
		
		// 设置通信路由
		this.setupCommunicationRoutes()
		
		if (this.config.debugMode) {
			console.log("MultiAgent Chat Interface initialized")
		}
	}

	/**
	 * 处理用户消息 - 主要入口点
	 * 保持与原Task类相同的接口
	 */
	public async processUserMessage(
		message: string, 
		images?: string[],
		callbacks?: ExecutionCallbacks
	): Promise<ClineMessage[]> {
		if (this.isProcessing) {
			throw new Error("Another request is currently being processed")
		}

		this.isProcessing = true
		const responses: ClineMessage[] = []

		try {
			// 1. 更新对话上下文
			this.contextManager.addMessage({
				role: "user",
				content: message
			})

			// 2. 路由用户请求
			const routingResult = await this.routeUserRequest(message)
			
			// 3. 显示路由结果给用户
			responses.push(this.createRoutingMessage(routingResult))

			// 4. 执行计划
			const executionResult = await this.executeRoutingResult(routingResult, callbacks)
			
			// 5. 收集和格式化结果
			const finalResponses = await this.collectExecutionResults(executionResult)
			responses.push(...finalResponses)

			// 6. 更新消息历史
			this.messageHistory.push(...responses)

			return responses

		} catch (error) {
			const errorMessage = this.createErrorMessage(error)
			responses.push(errorMessage)
			this.messageHistory.push(errorMessage)
			
			if (callbacks?.onError) {
				callbacks.onError(error instanceof Error ? error.message : String(error))
			}
			
			return responses

		} finally {
			this.isProcessing = false
		}
	}

	/**
	 * 路由用户请求
	 */
	private async routeUserRequest(userInput: string): Promise<RoutingResult> {
		const context = this.contextManager.getCurrentContext()
		return await this.taskRouter.route(userInput, context)
	}

	/**
	 * 执行路由结果
	 */
	private async executeRoutingResult(
		routingResult: RoutingResult, 
		callbacks?: ExecutionCallbacks
	): Promise<ExecutionStatus> {
		const context = this.contextManager.getCurrentContext()
		
		// 开始执行
		this.currentExecution = await this.taskOrchestrator.executePlan(
			routingResult.executionPlan.id, 
			context
		)

		// 设置进度回调
		if (callbacks?.onProgress) {
			const progressInterval = setInterval(() => {
				if (this.currentExecution) {
					callbacks.onProgress!(this.currentExecution.progress, this.currentExecution.status)
				}
			}, 1000)

			// 执行完成后清理定时器
			this.currentExecution.completedAt && clearInterval(progressInterval)
		}

		return this.currentExecution
	}

	/**
	 * 收集执行结果
	 */
	private async collectExecutionResults(execution: ExecutionStatus): Promise<ClineMessage[]> {
		const messages: ClineMessage[] = []

		// 处理每个任务的结果
		for (const [taskId, result] of execution.results) {
			if (result.success) {
				messages.push(this.createSuccessMessage(taskId, result))
				
				// 如果有工具响应，添加到消息中
				if (result.toolResponses) {
					for (const toolResponse of result.toolResponses) {
						messages.push(this.createToolResponseMessage(toolResponse))
					}
				}
			} else {
				messages.push(this.createTaskErrorMessage(taskId, result.error || "Unknown error"))
			}
		}

		// 添加执行摘要
		messages.push(this.createExecutionSummaryMessage(execution))

		return messages
	}

	/**
	 * 创建路由消息
	 */
	private createRoutingMessage(routingResult: RoutingResult): ClineMessage {
		const { intent, executionPlan } = routingResult
		
		return {
			ts: Date.now(),
			type: "say",
			say: "text",
			text: `🎯 **任务分析完成**

**意图识别**: ${this.getIntentDisplayName(intent.type)} (置信度: ${Math.round(intent.confidence * 100)}%)
**复杂度**: ${intent.complexity}
**预估时间**: ${intent.estimatedDuration} 分钟
**涉及Agent**: ${intent.suggestedAgents.join(", ")}

**执行计划**: 
- 总任务数: ${executionPlan.tasks.length}
- 预估总时长: ${executionPlan.estimatedDuration} 分钟

开始执行...`
		}
	}

	/**
	 * 创建成功消息
	 */
	private createSuccessMessage(taskId: string, result: AgentResult): ClineMessage {
		return {
			ts: Date.now(),
			type: "say",
			say: "text",
			text: `✅ **任务完成**: ${taskId}\n\n${JSON.stringify(result.data, null, 2)}`
		}
	}

	/**
	 * 创建工具响应消息
	 */
	private createToolResponseMessage(toolResponse: ToolResponse): ClineMessage {
		return {
			ts: Date.now(),
			type: "say",
			say: "tool",
			tool: toolResponse.tool,
			path: toolResponse.path,
			content: toolResponse.content
		}
	}

	/**
	 * 创建任务错误消息
	 */
	private createTaskErrorMessage(taskId: string, error: string): ClineMessage {
		return {
			ts: Date.now(),
			type: "say",
			say: "error",
			text: `❌ **任务失败**: ${taskId}\n\n错误: ${error}`
		}
	}

	/**
	 * 创建执行摘要消息
	 */
	private createExecutionSummaryMessage(execution: ExecutionStatus): ClineMessage {
		const duration = execution.completedAt && execution.startedAt 
			? Math.round((execution.completedAt.getTime() - execution.startedAt.getTime()) / 1000)
			: 0

		return {
			ts: Date.now(),
			type: "say",
			say: "text",
			text: `📊 **执行摘要**

**状态**: ${execution.status}
**进度**: ${execution.progress}%
**完成任务**: ${execution.completedTasks}/${execution.totalTasks}
**执行时间**: ${duration} 秒
${execution.errors.length > 0 ? `**错误**: ${execution.errors.length} 个` : ""}

${execution.status === "completed" ? "🎉 所有任务已成功完成！" : ""}`
		}
	}

	/**
	 * 创建错误消息
	 */
	private createErrorMessage(error: any): ClineMessage {
		return {
			ts: Date.now(),
			type: "say",
			say: "error",
			text: `❌ **系统错误**: ${error instanceof Error ? error.message : String(error)}`
		}
	}

	/**
	 * 获取意图显示名称
	 */
	private getIntentDisplayName(intentType: string): string {
		const displayNames: Record<string, string> = {
			code_generation: "代码生成",
			code_review: "代码审查",
			testing: "测试",
			debugging: "调试",
			refactoring: "重构",
			documentation: "文档编写",
			project_setup: "项目设置",
			analysis: "代码分析",
			general_question: "通用问题",
			multi_step_task: "多步骤任务"
		}
		
		return displayNames[intentType] || intentType
	}

	/**
	 * 设置事件处理器
	 */
	private setupEventHandlers(): void {
		// 任务编排器事件
		this.taskOrchestrator.on("taskAssigned", (taskId, agentId) => {
			this.emit("agentAssigned", taskId, agentId)
		})

		this.taskOrchestrator.on("taskCompleted", (taskId, result) => {
			this.emit("taskCompleted", taskId, result)
		})

		this.taskOrchestrator.on("taskFailed", (taskId, error) => {
			this.emit("taskFailed", taskId, error)
		})

		// 通信总线事件
		this.communicationBus.on("collaborationRequested", (request) => {
			if (this.config.debugMode) {
				console.log("Collaboration requested:", request)
			}
		})
	}

	/**
	 * 设置通信路由
	 */
	private setupCommunicationRoutes(): void {
		// 设置Agent间协作路由
		this.communicationBus.addMessageRoute({
			messageType: AgentMessageType.REQUEST_COLLABORATION,
			handler: async (message, context) => {
				// 处理协作请求
				if (this.config.debugMode) {
					console.log("Handling collaboration request:", message)
				}
			}
		})

		this.communicationBus.addMessageRoute({
			messageType: AgentMessageType.SHARE_RESULT,
			handler: async (message, context) => {
				// 处理结果分享
				this.contextManager.createArtifact(
					"analysis",
					"Agent Result",
					`Result from ${message.fromAgent}`,
					message.content,
					message.fromAgent
				)
			}
		})
	}

	/**
	 * 获取当前执行状态
	 */
	public getCurrentExecutionStatus(): ExecutionStatus | null {
		return this.currentExecution
	}

	/**
	 * 取消当前执行
	 */
	public cancelCurrentExecution(): boolean {
		if (this.currentExecution) {
			return this.taskOrchestrator.cancelExecution(this.currentExecution.planId)
		}
		return false
	}

	/**
	 * 获取消息历史
	 */
	public getMessageHistory(): ClineMessage[] {
		return [...this.messageHistory]
	}

	/**
	 * 获取Agent状态
	 */
	public getAgentStatuses(): any[] {
		return this.agentSystem.getRegistry().getAllAgentStatus()
	}

	/**
	 * 清理资源
	 */
	public async cleanup(): Promise<void> {
		this.communicationBus.cleanup()
		await this.agentSystem.cleanup()
		this.removeAllListeners()
	}

	/**
	 * 兼容性方法：模拟原Task类的say方法
	 */
	public async say(
		type: string, 
		text?: string, 
		images?: string[], 
		files?: string[]
	): Promise<void> {
		const message: ClineMessage = {
			ts: Date.now(),
			type: "say",
			say: type as any,
			text,
			images,
			files
		}
		
		this.messageHistory.push(message)
		this.emit("message", message)
	}

	/**
	 * 兼容性方法：模拟原Task类的ask方法
	 */
	public async ask(
		type: ClineAsk, 
		text?: string
	): Promise<any> {
		// 在多Agent环境中，大多数决策应该是自动的
		// 这里提供基本的用户交互支持
		return new Promise((resolve) => {
			const message: ClineMessage = {
				ts: Date.now(),
				type: "ask",
				ask: type,
				text
			}
			
			this.messageHistory.push(message)
			this.emit("ask", message, resolve)
		})
	}
}
