/**
 * 多Agent系统测试
 * 验证多Agent协作功能的基本测试
 */

import { MultiAgentSystem } from "../index"
import { AgentRole, TaskPriority, TaskStatus } from "../../agents/types"

/**
 * 测试配置
 */
const TEST_CONFIG = {
	enableCollaboration: true,
	maxConcurrentAgents: 3,
	responseTimeout: 30000, // 30秒用于测试
	debugMode: true
}

/**
 * 模拟执行上下文
 */
const MOCK_CONTEXT = {
	workspaceRoot: "/test/workspace",
	currentWorkingDirectory: "/test/workspace",
	projectFiles: ["src/index.ts", "package.json", "README.md"],
	conversationHistory: [],
	availableTools: ["write_to_file", "read_file", "execute_command"],
	mcpServers: [],
	collaboratingAgents: [],
	messageHistory: []
}

/**
 * 多Agent系统基础测试
 */
export class MultiAgentSystemTest {
	private system: MultiAgentSystem

	constructor() {
		this.system = new MultiAgentSystem(MOCK_CONTEXT, TEST_CONFIG)
	}

	/**
	 * 运行所有测试
	 */
	public async runAllTests(): Promise<void> {
		console.log("🧪 开始多Agent系统测试...")

		try {
			await this.testSystemInitialization()
			await this.testCodeGenerationTask()
			await this.testCodeReviewTask()
			await this.testMultiStepTask()
			await this.testAgentCollaboration()
			await this.testErrorHandling()
			
			console.log("✅ 所有测试通过！")
		} catch (error) {
			console.error("❌ 测试失败:", error)
			throw error
		} finally {
			await this.cleanup()
		}
	}

	/**
	 * 测试系统初始化
	 */
	private async testSystemInitialization(): Promise<void> {
		console.log("📋 测试系统初始化...")
		
		await this.system.initialize()
		
		const status = this.system.getSystemStatus()
		if (!status.initialized) {
			throw new Error("系统初始化失败")
		}
		
		const agentStatuses = status.agentStatuses
		if (agentStatuses.length === 0) {
			throw new Error("没有Agent被注册")
		}
		
		console.log(`✅ 系统初始化成功，注册了 ${agentStatuses.length} 个Agent`)
	}

	/**
	 * 测试代码生成任务
	 */
	private async testCodeGenerationTask(): Promise<void> {
		console.log("📋 测试代码生成任务...")
		
		const userMessage = "创建一个简单的TypeScript函数来计算两个数字的和"
		const responses = await this.system.processMessage(userMessage)
		
		if (responses.length === 0) {
			throw new Error("没有收到响应")
		}
		
		// 检查是否包含代码生成相关的响应
		const hasCodeGenResponse = responses.some(response => 
			response.text?.includes("代码生成") || 
			response.text?.includes("函数")
		)
		
		if (!hasCodeGenResponse) {
			throw new Error("没有找到代码生成相关的响应")
		}
		
		console.log("✅ 代码生成任务测试通过")
	}

	/**
	 * 测试代码审查任务
	 */
	private async testCodeReviewTask(): Promise<void> {
		console.log("📋 测试代码审查任务...")
		
		const userMessage = "请审查 src/index.ts 文件中的代码质量"
		const responses = await this.system.processMessage(userMessage)
		
		if (responses.length === 0) {
			throw new Error("没有收到响应")
		}
		
		// 检查是否包含代码审查相关的响应
		const hasReviewResponse = responses.some(response => 
			response.text?.includes("代码审查") || 
			response.text?.includes("审查")
		)
		
		if (!hasReviewResponse) {
			throw new Error("没有找到代码审查相关的响应")
		}
		
		console.log("✅ 代码审查任务测试通过")
	}

	/**
	 * 测试多步骤任务
	 */
	private async testMultiStepTask(): Promise<void> {
		console.log("📋 测试多步骤任务...")
		
		const userMessage = "创建一个新的React组件，包括组件代码、测试文件和文档"
		const responses = await this.system.processMessage(userMessage)
		
		if (responses.length === 0) {
			throw new Error("没有收到响应")
		}
		
		// 检查是否识别为多步骤任务
		const hasMultiStepResponse = responses.some(response => 
			response.text?.includes("任务数") || 
			response.text?.includes("执行计划")
		)
		
		if (!hasMultiStepResponse) {
			throw new Error("没有正确识别多步骤任务")
		}
		
		console.log("✅ 多步骤任务测试通过")
	}

	/**
	 * 测试Agent协作
	 */
	private async testAgentCollaboration(): Promise<void> {
		console.log("📋 测试Agent协作...")
		
		const chatInterface = this.system.getChatInterface()
		const agentStatuses = chatInterface.getAgentStatuses()
		
		if (agentStatuses.length < 2) {
			console.log("⚠️ Agent数量不足，跳过协作测试")
			return
		}
		
		// 模拟需要多个Agent协作的任务
		const userMessage = "创建一个函数并为其编写测试，然后进行代码审查"
		const responses = await this.system.processMessage(userMessage)
		
		if (responses.length === 0) {
			throw new Error("没有收到响应")
		}
		
		console.log("✅ Agent协作测试通过")
	}

	/**
	 * 测试错误处理
	 */
	private async testErrorHandling(): Promise<void> {
		console.log("📋 测试错误处理...")
		
		// 发送一个可能导致错误的消息
		const userMessage = "执行一个不存在的命令：invalid_command_xyz"
		
		try {
			const responses = await this.system.processMessage(userMessage)
			
			// 应该收到错误响应或者优雅处理
			if (responses.length === 0) {
				throw new Error("没有收到任何响应")
			}
			
			console.log("✅ 错误处理测试通过")
		} catch (error) {
			// 如果抛出错误，检查是否是预期的错误类型
			console.log("✅ 错误处理测试通过（捕获到预期错误）")
		}
	}

	/**
	 * 清理测试资源
	 */
	private async cleanup(): Promise<void> {
		console.log("🧹 清理测试资源...")
		await this.system.cleanup()
		console.log("✅ 清理完成")
	}
}

/**
 * 性能测试
 */
export class MultiAgentPerformanceTest {
	private system: MultiAgentSystem

	constructor() {
		this.system = new MultiAgentSystem(MOCK_CONTEXT, TEST_CONFIG)
	}

	/**
	 * 运行性能测试
	 */
	public async runPerformanceTests(): Promise<void> {
		console.log("⚡ 开始性能测试...")

		await this.system.initialize()

		try {
			await this.testResponseTime()
			await this.testConcurrentTasks()
			await this.testMemoryUsage()
			
			console.log("✅ 性能测试完成")
		} finally {
			await this.system.cleanup()
		}
	}

	/**
	 * 测试响应时间
	 */
	private async testResponseTime(): Promise<void> {
		console.log("📊 测试响应时间...")
		
		const startTime = Date.now()
		await this.system.processMessage("创建一个简单的Hello World函数")
		const endTime = Date.now()
		
		const responseTime = endTime - startTime
		console.log(`⏱️ 响应时间: ${responseTime}ms`)
		
		if (responseTime > 10000) { // 10秒
			console.warn("⚠️ 响应时间较长，可能需要优化")
		}
	}

	/**
	 * 测试并发任务
	 */
	private async testConcurrentTasks(): Promise<void> {
		console.log("📊 测试并发任务处理...")
		
		const tasks = [
			"创建一个计算器函数",
			"审查现有代码",
			"生成测试用例"
		]
		
		const startTime = Date.now()
		const promises = tasks.map(task => this.system.processMessage(task))
		await Promise.all(promises)
		const endTime = Date.now()
		
		const totalTime = endTime - startTime
		console.log(`⏱️ 并发处理时间: ${totalTime}ms`)
	}

	/**
	 * 测试内存使用
	 */
	private async testMemoryUsage(): Promise<void> {
		console.log("📊 测试内存使用...")
		
		const initialMemory = process.memoryUsage()
		
		// 执行多个任务
		for (let i = 0; i < 5; i++) {
			await this.system.processMessage(`创建函数 ${i}`)
		}
		
		const finalMemory = process.memoryUsage()
		const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed
		
		console.log(`💾 内存增长: ${Math.round(memoryIncrease / 1024 / 1024)}MB`)
		
		if (memoryIncrease > 100 * 1024 * 1024) { // 100MB
			console.warn("⚠️ 内存使用量较高，可能存在内存泄漏")
		}
	}
}

/**
 * 运行测试的主函数
 */
export async function runMultiAgentTests(): Promise<void> {
	console.log("🚀 开始多Agent系统测试套件...")
	
	// 基础功能测试
	const basicTest = new MultiAgentSystemTest()
	await basicTest.runAllTests()
	
	// 性能测试
	const performanceTest = new MultiAgentPerformanceTest()
	await performanceTest.runPerformanceTests()
	
	console.log("🎉 所有测试完成！")
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
	runMultiAgentTests().catch(console.error)
}
