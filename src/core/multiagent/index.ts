/**
 * 多Agent系统集成导出
 * 提供完整的多Agent协作平台功能
 */

// 核心Agent系统
export * from "../agents"

// 任务编排
export { TaskOrchestrator, ExecutionPlan, TaskDecompositionStrategy } from "../orchestration/TaskOrchestrator"

// 通信系统
export { AgentCommunicationBus } from "../communication/AgentCommunicationBus"

// 上下文管理
export { SharedContextManager, ProjectContext, ConversationContext, WorkArtifact } from "../context/SharedContextManager"

// 任务路由
export { TaskRouter, UserIntentType, IntentAnalysis, RoutingResult } from "../routing/TaskRouter"

// 统一对话界面
export { MultiAgentChatInterface, MultiAgentChatConfig } from "./MultiAgentChatInterface"

/**
 * 多Agent系统管理器
 * 提供完整的多Agent协作平台
 */
export class MultiAgentSystem {
	private chatInterface: MultiAgentChatInterface
	private initialized: boolean = false

	constructor(initialContext: any = {}, config: any = {}) {
		this.chatInterface = new MultiAgentChatInterface(initialContext, config)
	}

	/**
	 * 初始化系统
	 */
	public async initialize(): Promise<void> {
		if (this.initialized) {
			return
		}

		await this.chatInterface.initialize()
		this.initialized = true
	}

	/**
	 * 获取对话界面
	 */
	public getChatInterface(): MultiAgentChatInterface {
		return this.chatInterface
	}

	/**
	 * 处理用户消息
	 */
	public async processMessage(message: string, images?: string[]): Promise<any[]> {
		if (!this.initialized) {
			await this.initialize()
		}

		return await this.chatInterface.processUserMessage(message, images)
	}

	/**
	 * 获取系统状态
	 */
	public getSystemStatus(): any {
		return {
			initialized: this.initialized,
			currentExecution: this.chatInterface.getCurrentExecutionStatus(),
			agentStatuses: this.chatInterface.getAgentStatuses(),
			messageHistory: this.chatInterface.getMessageHistory()
		}
	}

	/**
	 * 清理资源
	 */
	public async cleanup(): Promise<void> {
		await this.chatInterface.cleanup()
		this.initialized = false
	}
}

// 默认导出
export default MultiAgentSystem
