/**
 * Cline多Agent适配器
 * 将多Agent系统集成到现有Cline架构中的适配器
 */

import * as vscode from "vscode"
import { Anthropic } from "@anthropic-ai/sdk"

import { MultiAgentSystem } from "../index"
import { ExecutionContext } from "../../agents/types"
import { ClineMessage, ClineAsk } from "@shared/ExtensionMessage"

// 导入现有Cline组件
import { Task } from "../../task"
import { ApiHandler } from "../../../api"
import { TerminalManager } from "../../terminal/TerminalManager"
import { BrowserSession } from "../../browser/BrowserSession"
import { ContextManager } from "../../context/ContextManager"
import { McpHub } from "../../../services/mcp/McpHub"
import { WorkspaceTracker } from "../../workspace/WorkspaceTracker"

/**
 * 多Agent模式配置
 */
export interface MultiAgentModeConfig {
	enabled: boolean
	fallbackToSingleAgent: boolean
	debugMode: boolean
	maxConcurrentAgents: number
	collaborationTimeout: number
}

/**
 * Cline多Agent适配器
 * 在保持现有接口兼容性的同时，提供多Agent协作能力
 */
export class ClineMultiAgentAdapter {
	private multiAgentSystem: MultiAgentSystem
	private originalTask: Task
	private config: MultiAgentModeConfig
	private isMultiAgentMode: boolean = false

	constructor(
		originalTask: Task,
		config: Partial<MultiAgentModeConfig> = {}
	) {
		this.originalTask = originalTask
		this.config = {
			enabled: true,
			fallbackToSingleAgent: true,
			debugMode: false,
			maxConcurrentAgents: 3,
			collaborationTimeout: 300000, // 5分钟
			...config
		}

		// 初始化多Agent系统
		this.multiAgentSystem = new MultiAgentSystem(
			this.createExecutionContext(),
			{
				enableCollaboration: true,
				maxConcurrentAgents: this.config.maxConcurrentAgents,
				responseTimeout: this.config.collaborationTimeout,
				debugMode: this.config.debugMode
			}
		)
	}

	/**
	 * 初始化适配器
	 */
	public async initialize(): Promise<void> {
		if (this.config.enabled) {
			try {
				await this.multiAgentSystem.initialize()
				this.isMultiAgentMode = true
				
				if (this.config.debugMode) {
					console.log("Multi-Agent mode enabled")
				}
			} catch (error) {
				console.error("Failed to initialize multi-agent system:", error)
				
				if (this.config.fallbackToSingleAgent) {
					console.log("Falling back to single-agent mode")
					this.isMultiAgentMode = false
				} else {
					throw error
				}
			}
		}
	}

	/**
	 * 处理用户消息 - 主要适配方法
	 */
	public async processUserMessage(
		message: string,
		images?: string[]
	): Promise<ClineMessage[]> {
		if (this.isMultiAgentMode) {
			try {
				// 使用多Agent系统处理
				return await this.processWithMultiAgent(message, images)
			} catch (error) {
				console.error("Multi-agent processing failed:", error)
				
				if (this.config.fallbackToSingleAgent) {
					console.log("Falling back to single-agent processing")
					return await this.processWithSingleAgent(message, images)
				} else {
					throw error
				}
			}
		} else {
			// 使用原始单Agent处理
			return await this.processWithSingleAgent(message, images)
		}
	}

	/**
	 * 使用多Agent系统处理消息
	 */
	private async processWithMultiAgent(
		message: string,
		images?: string[]
	): Promise<ClineMessage[]> {
		const chatInterface = this.multiAgentSystem.getChatInterface()
		
		// 设置进度回调
		const callbacks = {
			onProgress: (progress: number, status: string) => {
				this.originalTask.say("text", `🔄 进度: ${progress}% - ${status}`)
			},
			onAgentAssigned: (taskId: string, agentId: string) => {
				if (this.config.debugMode) {
					this.originalTask.say("text", `🤖 任务 ${taskId} 分配给 Agent: ${agentId}`)
				}
			},
			onTaskCompleted: (taskId: string, result: any) => {
				if (this.config.debugMode) {
					this.originalTask.say("text", `✅ 任务完成: ${taskId}`)
				}
			},
			onError: (error: string) => {
				this.originalTask.say("error", `❌ 多Agent执行错误: ${error}`)
			}
		}

		// 处理消息
		const responses = await chatInterface.processUserMessage(message, images, callbacks)
		
		// 转换响应格式以兼容现有界面
		return this.convertResponsesToClineMessages(responses)
	}

	/**
	 * 使用原始单Agent处理消息
	 */
	private async processWithSingleAgent(
		message: string,
		images?: string[]
	): Promise<ClineMessage[]> {
		// 这里需要调用原始Task的处理逻辑
		// 由于原始Task类的复杂性，这里提供一个简化的实现
		
		// 模拟原始处理流程
		await this.originalTask.say("text", "🔄 使用单Agent模式处理...")
		
		// 这里应该调用原始的Task.initiateTaskLoop或相关方法
		// 但由于架构复杂性，暂时返回模拟响应
		return [{
			ts: Date.now(),
			type: "say",
			say: "text",
			text: "单Agent模式处理完成（需要集成原始Task逻辑）"
		}]
	}

	/**
	 * 转换响应格式
	 */
	private convertResponsesToClineMessages(responses: any[]): ClineMessage[] {
		return responses.map(response => {
			// 确保响应格式符合ClineMessage接口
			return {
				ts: response.ts || Date.now(),
				type: response.type || "say",
				say: response.say || "text",
				text: response.text,
				images: response.images,
				files: response.files,
				tool: response.tool,
				path: response.path,
				content: response.content
			}
		})
	}

	/**
	 * 创建执行上下文
	 */
	private createExecutionContext(): ExecutionContext {
		// 从原始Task中提取上下文信息
		const context: ExecutionContext = {
			workspaceRoot: this.originalTask.getCwd?.() || process.cwd(),
			currentWorkingDirectory: this.originalTask.getCwd?.() || process.cwd(),
			projectFiles: [], // 需要从WorkspaceTracker获取
			conversationHistory: [], // 需要从原始Task获取
			currentTask: undefined,
			relatedTasks: [],
			sharedState: {},
			availableTools: [
				"write_to_file",
				"read_file", 
				"str_replace_editor",
				"execute_command",
				"list_files",
				"use_mcp_tool"
			],
			mcpServers: [], // 需要从McpHub获取
			collaboratingAgents: [],
			messageHistory: []
		}

		return context
	}

	/**
	 * 切换到多Agent模式
	 */
	public async enableMultiAgentMode(): Promise<boolean> {
		if (!this.isMultiAgentMode) {
			try {
				await this.multiAgentSystem.initialize()
				this.isMultiAgentMode = true
				
				await this.originalTask.say("text", "🤖 多Agent协作模式已启用")
				return true
			} catch (error) {
				await this.originalTask.say("error", `启用多Agent模式失败: ${error}`)
				return false
			}
		}
		return true
	}

	/**
	 * 切换到单Agent模式
	 */
	public async disableMultiAgentMode(): Promise<void> {
		if (this.isMultiAgentMode) {
			await this.multiAgentSystem.cleanup()
			this.isMultiAgentMode = false
			
			await this.originalTask.say("text", "🔄 已切换回单Agent模式")
		}
	}

	/**
	 * 获取当前模式状态
	 */
	public getStatus(): {
		isMultiAgentMode: boolean
		systemStatus?: any
	} {
		return {
			isMultiAgentMode: this.isMultiAgentMode,
			systemStatus: this.isMultiAgentMode ? this.multiAgentSystem.getSystemStatus() : undefined
		}
	}

	/**
	 * 获取Agent状态信息
	 */
	public getAgentStatuses(): any[] {
		if (this.isMultiAgentMode) {
			return this.multiAgentSystem.getChatInterface().getAgentStatuses()
		}
		return []
	}

	/**
	 * 取消当前执行
	 */
	public cancelCurrentExecution(): boolean {
		if (this.isMultiAgentMode) {
			return this.multiAgentSystem.getChatInterface().cancelCurrentExecution()
		}
		return false
	}

	/**
	 * 清理资源
	 */
	public async cleanup(): Promise<void> {
		if (this.isMultiAgentMode) {
			await this.multiAgentSystem.cleanup()
		}
	}

	/**
	 * 兼容性方法：代理原始Task的say方法
	 */
	public async say(
		type: string,
		text?: string,
		images?: string[],
		files?: string[]
	): Promise<void> {
		if (this.isMultiAgentMode) {
			await this.multiAgentSystem.getChatInterface().say(type, text, images, files)
		} else {
			await this.originalTask.say(type as any, text, images, files)
		}
	}

	/**
	 * 兼容性方法：代理原始Task的ask方法
	 */
	public async ask(type: ClineAsk, text?: string): Promise<any> {
		if (this.isMultiAgentMode) {
			return await this.multiAgentSystem.getChatInterface().ask(type, text)
		} else {
			return await this.originalTask.ask(type, text)
		}
	}

	/**
	 * 获取配置
	 */
	public getConfig(): MultiAgentModeConfig {
		return { ...this.config }
	}

	/**
	 * 更新配置
	 */
	public updateConfig(newConfig: Partial<MultiAgentModeConfig>): void {
		this.config = { ...this.config, ...newConfig }
	}
}

/**
 * 工厂函数：创建多Agent适配器
 */
export function createMultiAgentAdapter(
	originalTask: Task,
	config?: Partial<MultiAgentModeConfig>
): ClineMultiAgentAdapter {
	return new ClineMultiAgentAdapter(originalTask, config)
}

/**
 * 检查是否支持多Agent模式
 */
export function isMultiAgentSupported(): boolean {
	try {
		// 检查必要的依赖和环境
		return true
	} catch (error) {
		console.error("Multi-agent mode not supported:", error)
		return false
	}
}
