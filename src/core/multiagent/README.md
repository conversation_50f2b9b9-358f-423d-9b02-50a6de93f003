# Cline 多智能体协作系统

## 概述

Cline多智能体协作系统将原本的单一智能体架构升级为专业化的多智能体协作平台。每个Agent专注于特定领域，通过智能协作完成复杂任务。

## 架构特点

### 🎯 专业化分工
- **代码生成Agent**: 专注代码编写、文件创建和重构
- **代码审查Agent**: 专注代码分析、bug检测和质量检查  
- **测试Agent**: 专注测试生成、执行和覆盖率分析
- **调试Agent**: 专注问题诊断和修复
- **文档Agent**: 专注文档生成和维护

### 🤝 智能协作
- **任务分解**: 自动将复杂任务分解为子任务
- **Agent选择**: 智能选择最适合的Agent执行任务
- **协作通信**: Agent间可以请求协助和分享结果
- **负载均衡**: 动态分配任务以优化性能

### 🧠 智能路由
- **意图识别**: 分析用户请求，识别任务类型和复杂度
- **执行计划**: 生成优化的执行计划和依赖关系
- **策略选择**: 根据任务特点选择最佳执行策略

## 快速开始

### 基本使用

```typescript
import { MultiAgentSystem } from './core/multiagent'

// 创建多Agent系统
const system = new MultiAgentSystem({
  workspaceRoot: '/path/to/project',
  currentWorkingDirectory: '/path/to/project'
})

// 初始化系统
await system.initialize()

// 处理用户请求
const responses = await system.processMessage(
  "创建一个用户认证模块，包括登录、注册功能，并编写相应的测试"
)

console.log(responses)
```

### 与现有Cline集成

```typescript
import { ClineMultiAgentAdapter } from './core/multiagent/integration/ClineMultiAgentAdapter'

// 创建适配器
const adapter = new ClineMultiAgentAdapter(originalTask, {
  enabled: true,
  fallbackToSingleAgent: true,
  debugMode: false
})

// 初始化
await adapter.initialize()

// 处理消息（保持原有接口）
const responses = await adapter.processUserMessage(
  "重构这个函数并添加单元测试"
)
```

## 核心组件

### 1. Agent系统 (`src/core/agents/`)

#### Agent接口
```typescript
interface Agent {
  id: string
  name: string
  role: AgentRole
  capabilities: AgentCapability[]
  
  canHandle(task: AgentTask): boolean
  execute(task: AgentTask, context: ExecutionContext): Promise<AgentResult>
  collaborate(message: AgentMessage, context: ExecutionContext): Promise<AgentResult>
}
```

#### 专业化Agent
- `CodeGenerationAgent`: 代码生成专家
- `CodeReviewAgent`: 代码审查专家  
- `TestingAgent`: 测试专家

### 2. 任务编排 (`src/core/orchestration/`)

#### TaskOrchestrator
负责任务分解、分发和执行协调：

```typescript
// 分解用户请求
const plan = await orchestrator.decomposeUserRequest(userInput, context)

// 执行计划
const status = await orchestrator.executePlan(plan.id, context)
```

### 3. 通信系统 (`src/core/communication/`)

#### AgentCommunicationBus
提供Agent间的消息传递和协作：

```typescript
// 发送消息
await bus.sendMessage(fromAgent, toAgent, messageType, content, context)

// 广播消息
await bus.broadcast(fromAgent, messageType, content, context)

// 请求协作
await bus.requestCollaboration(collaborationRequest, context)
```

### 4. 上下文管理 (`src/core/context/`)

#### SharedContextManager
管理共享的项目上下文和工作成果：

```typescript
// 更新项目上下文
contextManager.updateProjectContext({ projectFiles: newFiles })

// 创建工作成果
const artifact = contextManager.createArtifact(
  'code', 
  'User Service', 
  'User authentication service',
  codeContent,
  'CodeGenerationAgent'
)
```

### 5. 智能路由 (`src/core/routing/`)

#### TaskRouter
分析用户意图并生成执行计划：

```typescript
// 路由用户请求
const result = await router.route(userInput, context)

console.log(result.intent.type) // 'code_generation'
console.log(result.executionPlan.tasks.length) // 3
```

## 配置选项

### MultiAgentChatConfig
```typescript
interface MultiAgentChatConfig {
  enableCollaboration: boolean      // 启用Agent协作
  maxConcurrentAgents: number       // 最大并发Agent数
  responseTimeout: number           // 响应超时时间
  autoApprovalEnabled: boolean      // 自动批准模式
  debugMode: boolean               // 调试模式
}
```

### AgentConfig
```typescript
interface AgentConfig {
  maxConcurrentTasks: number        // 最大并发任务数
  timeoutMs: number                // 任务超时时间
  retryAttempts: number            // 重试次数
  autoApprovalEnabled: boolean     // 自动批准
  collaborationEnabled: boolean    // 启用协作
  preferredTools: string[]         // 偏好工具
}
```

## 使用场景

### 1. 复杂功能开发
```
用户: "创建一个完整的博客系统"

系统分解为:
1. 代码生成Agent: 创建基础结构和API
2. 测试Agent: 生成单元测试和集成测试
3. 代码审查Agent: 审查代码质量
4. 文档Agent: 生成API文档
```

### 2. 代码质量提升
```
用户: "优化这个项目的代码质量"

系统分解为:
1. 代码审查Agent: 分析现有代码问题
2. 重构Agent: 执行代码重构
3. 测试Agent: 补充测试覆盖
4. 文档Agent: 更新文档
```

### 3. Bug修复
```
用户: "修复登录功能的问题"

系统分解为:
1. 调试Agent: 诊断问题根因
2. 代码生成Agent: 实施修复
3. 测试Agent: 验证修复效果
4. 代码审查Agent: 审查修复质量
```

## 性能优化

### 1. 并行执行
- 无依赖的任务自动并行执行
- 智能负载均衡
- 动态Agent调度

### 2. 缓存机制
- 上下文缓存
- 结果缓存
- Agent状态缓存

### 3. 资源管理
- 自动资源清理
- 内存使用监控
- 超时保护

## 测试

### 运行测试
```bash
# 基础功能测试
npm run test:multiagent

# 性能测试
npm run test:multiagent:performance

# 集成测试
npm run test:multiagent:integration
```

### 测试覆盖
- Agent功能测试
- 协作机制测试
- 错误处理测试
- 性能基准测试

## 故障排除

### 常见问题

1. **Agent初始化失败**
   - 检查依赖是否正确安装
   - 验证配置文件格式
   - 查看错误日志

2. **任务执行超时**
   - 增加超时时间配置
   - 检查任务复杂度
   - 启用调试模式

3. **协作失败**
   - 验证Agent状态
   - 检查通信总线
   - 查看协作日志

### 调试模式
```typescript
const system = new MultiAgentSystem(context, {
  debugMode: true  // 启用详细日志
})
```

## 扩展开发

### 创建自定义Agent
```typescript
class CustomAgent extends BaseAgent {
  constructor() {
    super("Custom Agent", AgentRole.CUSTOM, ["custom-capability"])
  }
  
  protected async executeTask(task: AgentTask, context: ExecutionContext): Promise<AgentResult> {
    // 实现自定义逻辑
    return this.createSuccessResult(result)
  }
}
```

### 注册自定义Agent
```typescript
const factory = AgentFactory.getInstance()
const registry = new AgentRegistry()

const customAgent = new CustomAgent()
registry.registerAgent(customAgent)
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 编写测试
4. 提交代码
5. 创建Pull Request

## 许可证

与Cline主项目保持一致的许可证。
