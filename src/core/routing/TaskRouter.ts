/**
 * 智能任务路由器
 * 负责意图识别、执行计划生成和任务路由
 */

import { 
	ExecutionContext, 
	AgentCapability, 
	TaskPriority,
	AgentRole
} from "../agents/types"
import { ExecutionPlan, TaskDecompositionStrategy } from "../orchestration/TaskOrchestrator"
import { AgentRegistry } from "../agents/AgentRegistry"
import { TaskOrchestrator } from "../orchestration/TaskOrchestrator"

/**
 * 用户意图类型
 */
export enum UserIntentType {
	CODE_GENERATION = "code_generation",
	CODE_REVIEW = "code_review",
	TESTING = "testing",
	DEBUGGING = "debugging",
	REFACTORING = "refactoring",
	DOCUMENTATION = "documentation",
	PROJECT_SETUP = "project_setup",
	ANALYSIS = "analysis",
	GENERAL_QUESTION = "general_question",
	MULTI_STEP_TASK = "multi_step_task"
}

/**
 * 意图分析结果
 */
export interface IntentAnalysis {
	type: UserIntentType
	confidence: number
	entities: IntentEntity[]
	requiredCapabilities: AgentCapability[]
	suggestedAgents: AgentRole[]
	complexity: "low" | "medium" | "high"
	urgency: "low" | "medium" | "high"
	estimatedDuration: number // 分钟
	parameters: Record<string, any>
}

/**
 * 意图实体
 */
export interface IntentEntity {
	type: "file" | "function" | "class" | "technology" | "action" | "location"
	value: string
	confidence: number
	startIndex?: number
	endIndex?: number
}

/**
 * 路由结果
 */
export interface RoutingResult {
	intent: IntentAnalysis
	executionPlan: ExecutionPlan
	recommendedStrategy: TaskDecompositionStrategy
	estimatedCost: number
	alternatives?: RoutingResult[]
}

/**
 * 意图识别规则
 */
interface IntentRule {
	patterns: RegExp[]
	type: UserIntentType
	requiredCapabilities: AgentCapability[]
	suggestedAgents: AgentRole[]
	complexity: "low" | "medium" | "high"
	extractEntities?: (text: string) => IntentEntity[]
}

/**
 * 智能任务路由器
 * 分析用户输入，识别意图，生成执行计划
 */
export class TaskRouter {
	private agentRegistry: AgentRegistry
	private taskOrchestrator: TaskOrchestrator
	private intentRules: IntentRule[]

	constructor(agentRegistry: AgentRegistry, taskOrchestrator: TaskOrchestrator) {
		this.agentRegistry = agentRegistry
		this.taskOrchestrator = taskOrchestrator
		this.intentRules = this.initializeIntentRules()
	}

	/**
	 * 路由用户请求
	 */
	public async route(userInput: string, context: ExecutionContext): Promise<RoutingResult> {
		// 1. 分析用户意图
		const intent = await this.analyzeIntent(userInput, context)
		
		// 2. 生成执行计划
		const executionPlan = await this.taskOrchestrator.decomposeUserRequest(
			userInput, 
			context, 
			this.selectDecompositionStrategy(intent)
		)
		
		// 3. 选择推荐策略
		const recommendedStrategy = this.selectDecompositionStrategy(intent)
		
		// 4. 估算成本
		const estimatedCost = this.estimateCost(intent, executionPlan)
		
		// 5. 生成替代方案（可选）
		const alternatives = await this.generateAlternatives(userInput, context, intent)

		return {
			intent,
			executionPlan,
			recommendedStrategy,
			estimatedCost,
			alternatives
		}
	}

	/**
	 * 分析用户意图
	 */
	private async analyzeIntent(userInput: string, context: ExecutionContext): Promise<IntentAnalysis> {
		const text = userInput.toLowerCase()
		let bestMatch: IntentRule | null = null
		let bestScore = 0

		// 使用规则匹配
		for (const rule of this.intentRules) {
			const score = this.calculateRuleScore(text, rule)
			if (score > bestScore) {
				bestScore = score
				bestMatch = rule
			}
		}

		if (!bestMatch) {
			// 默认为通用问题
			bestMatch = this.getDefaultRule()
		}

		// 提取实体
		const entities = bestMatch.extractEntities ? bestMatch.extractEntities(userInput) : []
		
		// 分析紧急程度
		const urgency = this.analyzeUrgency(text)
		
		// 估算持续时间
		const estimatedDuration = this.estimateDuration(bestMatch.type, bestMatch.complexity)

		return {
			type: bestMatch.type,
			confidence: bestScore,
			entities,
			requiredCapabilities: bestMatch.requiredCapabilities,
			suggestedAgents: bestMatch.suggestedAgents,
			complexity: bestMatch.complexity,
			urgency,
			estimatedDuration,
			parameters: this.extractParameters(userInput, entities, context)
		}
	}

	/**
	 * 初始化意图识别规则
	 */
	private initializeIntentRules(): IntentRule[] {
		return [
			// 代码生成
			{
				patterns: [
					/create|generate|write|build|implement|make/i,
					/file|function|class|component|module/i
				],
				type: UserIntentType.CODE_GENERATION,
				requiredCapabilities: ["code-writing", "file-creation"],
				suggestedAgents: [AgentRole.CODE_GENERATION],
				complexity: "medium",
				extractEntities: (text) => this.extractCodeEntities(text)
			},
			
			// 代码审查
			{
				patterns: [
					/review|check|analyze|examine|audit/i,
					/code|quality|bug|issue|problem/i
				],
				type: UserIntentType.CODE_REVIEW,
				requiredCapabilities: ["code-analysis", "bug-detection"],
				suggestedAgents: [AgentRole.CODE_REVIEW],
				complexity: "low",
				extractEntities: (text) => this.extractFileEntities(text)
			},
			
			// 测试
			{
				patterns: [
					/test|testing|spec|unit test|integration test/i
				],
				type: UserIntentType.TESTING,
				requiredCapabilities: ["test-generation", "test-execution"],
				suggestedAgents: [AgentRole.TESTING],
				complexity: "medium",
				extractEntities: (text) => this.extractTestEntities(text)
			},
			
			// 调试
			{
				patterns: [
					/debug|fix|solve|error|bug|issue|problem/i,
					/not working|broken|failing/i
				],
				type: UserIntentType.DEBUGGING,
				requiredCapabilities: ["debugging", "bug-detection"],
				suggestedAgents: [AgentRole.DEBUGGING, AgentRole.CODE_REVIEW],
				complexity: "high",
				extractEntities: (text) => this.extractErrorEntities(text)
			},
			
			// 重构
			{
				patterns: [
					/refactor|improve|optimize|clean up|restructure/i,
					/performance|maintainability|readability/i
				],
				type: UserIntentType.REFACTORING,
				requiredCapabilities: ["refactoring", "code-analysis"],
				suggestedAgents: [AgentRole.REFACTORING, AgentRole.CODE_REVIEW],
				complexity: "high",
				extractEntities: (text) => this.extractCodeEntities(text)
			},
			
			// 文档
			{
				patterns: [
					/document|documentation|comment|readme|guide/i,
					/explain|describe|how to/i
				],
				type: UserIntentType.DOCUMENTATION,
				requiredCapabilities: ["documentation-generation"],
				suggestedAgents: [AgentRole.DOCUMENTATION],
				complexity: "low",
				extractEntities: (text) => this.extractDocumentationEntities(text)
			},
			
			// 项目设置
			{
				patterns: [
					/setup|initialize|configure|install|scaffold/i,
					/project|workspace|environment/i
				],
				type: UserIntentType.PROJECT_SETUP,
				requiredCapabilities: ["project-planning", "file-creation"],
				suggestedAgents: [AgentRole.PROJECT_MANAGEMENT, AgentRole.CODE_GENERATION],
				complexity: "medium",
				extractEntities: (text) => this.extractTechnologyEntities(text)
			},
			
			// 分析
			{
				patterns: [
					/analyze|analysis|report|metrics|statistics/i,
					/performance|security|complexity/i
				],
				type: UserIntentType.ANALYSIS,
				requiredCapabilities: ["code-analysis", "performance-analysis"],
				suggestedAgents: [AgentRole.CODE_REVIEW, AgentRole.SECURITY_ANALYSIS],
				complexity: "medium",
				extractEntities: (text) => this.extractAnalysisEntities(text)
			}
		]
	}

	/**
	 * 计算规则匹配分数
	 */
	private calculateRuleScore(text: string, rule: IntentRule): number {
		let score = 0
		let matchCount = 0

		for (const pattern of rule.patterns) {
			if (pattern.test(text)) {
				matchCount++
			}
		}

		// 基础分数：匹配的模式数量 / 总模式数量
		score = matchCount / rule.patterns.length

		// 加权：如果所有模式都匹配，给予额外分数
		if (matchCount === rule.patterns.length) {
			score += 0.2
		}

		return Math.min(score, 1.0)
	}

	/**
	 * 获取默认规则
	 */
	private getDefaultRule(): IntentRule {
		return {
			patterns: [],
			type: UserIntentType.GENERAL_QUESTION,
			requiredCapabilities: [],
			suggestedAgents: [AgentRole.GENERAL_ASSISTANT],
			complexity: "low"
		}
	}

	/**
	 * 分析紧急程度
	 */
	private analyzeUrgency(text: string): "low" | "medium" | "high" {
		const urgentKeywords = /urgent|asap|immediately|critical|emergency|now/i
		const mediumKeywords = /soon|quick|fast|priority/i

		if (urgentKeywords.test(text)) {
			return "high"
		} else if (mediumKeywords.test(text)) {
			return "medium"
		} else {
			return "low"
		}
	}

	/**
	 * 估算持续时间
	 */
	private estimateDuration(type: UserIntentType, complexity: string): number {
		const baseTime = {
			[UserIntentType.CODE_GENERATION]: 20,
			[UserIntentType.CODE_REVIEW]: 10,
			[UserIntentType.TESTING]: 15,
			[UserIntentType.DEBUGGING]: 30,
			[UserIntentType.REFACTORING]: 45,
			[UserIntentType.DOCUMENTATION]: 15,
			[UserIntentType.PROJECT_SETUP]: 30,
			[UserIntentType.ANALYSIS]: 20,
			[UserIntentType.GENERAL_QUESTION]: 5,
			[UserIntentType.MULTI_STEP_TASK]: 60
		}

		const complexityMultiplier = {
			low: 0.5,
			medium: 1.0,
			high: 2.0
		}

		return Math.round(baseTime[type] * complexityMultiplier[complexity])
	}

	/**
	 * 提取参数
	 */
	private extractParameters(
		userInput: string, 
		entities: IntentEntity[], 
		context: ExecutionContext
	): Record<string, any> {
		const parameters: Record<string, any> = {}

		// 从实体中提取参数
		entities.forEach(entity => {
			if (entity.type === "file") {
				parameters.filePath = entity.value
			} else if (entity.type === "function") {
				parameters.functionName = entity.value
			} else if (entity.type === "technology") {
				parameters.technology = entity.value
			}
		})

		// 从上下文中提取参数
		if (context.currentWorkingDirectory) {
			parameters.workingDirectory = context.currentWorkingDirectory
		}

		return parameters
	}

	/**
	 * 选择分解策略
	 */
	private selectDecompositionStrategy(intent: IntentAnalysis): TaskDecompositionStrategy {
		if (intent.complexity === "high" || intent.type === UserIntentType.MULTI_STEP_TASK) {
			return TaskDecompositionStrategy.HYBRID
		} else if (intent.urgency === "high") {
			return TaskDecompositionStrategy.PARALLEL
		} else {
			return TaskDecompositionStrategy.CAPABILITY_BASED
		}
	}

	/**
	 * 估算成本
	 */
	private estimateCost(intent: IntentAnalysis, plan: ExecutionPlan): number {
		// 简化的成本估算：基于任务数量和复杂度
		const baseCost = plan.tasks.length * 10
		const complexityMultiplier = {
			low: 1,
			medium: 1.5,
			high: 2.5
		}
		
		return Math.round(baseCost * complexityMultiplier[intent.complexity])
	}

	/**
	 * 生成替代方案
	 */
	private async generateAlternatives(
		userInput: string, 
		context: ExecutionContext, 
		primaryIntent: IntentAnalysis
	): Promise<RoutingResult[]> {
		// 简化实现：如果是高复杂度任务，提供简化版本
		if (primaryIntent.complexity === "high") {
			const simplifiedIntent = {
				...primaryIntent,
				complexity: "medium" as const,
				estimatedDuration: Math.round(primaryIntent.estimatedDuration * 0.6)
			}

			const alternativePlan = await this.taskOrchestrator.decomposeUserRequest(
				userInput,
				context,
				TaskDecompositionStrategy.SEQUENTIAL
			)

			return [{
				intent: simplifiedIntent,
				executionPlan: alternativePlan,
				recommendedStrategy: TaskDecompositionStrategy.SEQUENTIAL,
				estimatedCost: this.estimateCost(simplifiedIntent, alternativePlan)
			}]
		}

		return []
	}

	// 实体提取方法
	private extractCodeEntities(text: string): IntentEntity[] {
		const entities: IntentEntity[] = []
		
		// 提取文件路径
		const filePattern = /[\w\/\-\.]+\.(js|ts|py|java|cpp|c|h|css|html|json|md)/gi
		let match
		while ((match = filePattern.exec(text)) !== null) {
			entities.push({
				type: "file",
				value: match[0],
				confidence: 0.9,
				startIndex: match.index,
				endIndex: match.index + match[0].length
			})
		}

		// 提取函数名
		const functionPattern = /function\s+(\w+)|(\w+)\s*\(/gi
		while ((match = functionPattern.exec(text)) !== null) {
			entities.push({
				type: "function",
				value: match[1] || match[2],
				confidence: 0.7
			})
		}

		return entities
	}

	private extractFileEntities(text: string): IntentEntity[] {
		return this.extractCodeEntities(text).filter(e => e.type === "file")
	}

	private extractTestEntities(text: string): IntentEntity[] {
		const entities = this.extractCodeEntities(text)
		
		// 添加测试相关的实体
		const testPattern = /(unit|integration|e2e|performance)\s+test/gi
		let match
		while ((match = testPattern.exec(text)) !== null) {
			entities.push({
				type: "action",
				value: match[0],
				confidence: 0.8
			})
		}

		return entities
	}

	private extractErrorEntities(text: string): IntentEntity[] {
		const entities: IntentEntity[] = []
		
		// 提取错误信息
		const errorPattern = /(error|exception|failure):\s*([^\n]+)/gi
		let match
		while ((match = errorPattern.exec(text)) !== null) {
			entities.push({
				type: "action",
				value: match[2],
				confidence: 0.9
			})
		}

		return entities
	}

	private extractDocumentationEntities(text: string): IntentEntity[] {
		const entities: IntentEntity[] = []
		
		// 提取文档类型
		const docPattern = /(readme|api|guide|tutorial|documentation)/gi
		let match
		while ((match = docPattern.exec(text)) !== null) {
			entities.push({
				type: "action",
				value: match[0],
				confidence: 0.8
			})
		}

		return entities
	}

	private extractTechnologyEntities(text: string): IntentEntity[] {
		const entities: IntentEntity[] = []
		
		// 提取技术栈
		const techPattern = /(react|vue|angular|node|python|java|typescript|javascript|docker|kubernetes)/gi
		let match
		while ((match = techPattern.exec(text)) !== null) {
			entities.push({
				type: "technology",
				value: match[0],
				confidence: 0.9
			})
		}

		return entities
	}

	private extractAnalysisEntities(text: string): IntentEntity[] {
		const entities: IntentEntity[] = []
		
		// 提取分析类型
		const analysisPattern = /(performance|security|complexity|quality|coverage)/gi
		let match
		while ((match = analysisPattern.exec(text)) !== null) {
			entities.push({
				type: "action",
				value: match[0],
				confidence: 0.8
			})
		}

		return entities
	}
}
