/**
 * Agent通信总线
 * 负责Agent间的消息传递、广播和状态同步
 */

import { EventEmitter } from "events"
import { 
	Agent, 
	AgentMessage, 
	AgentMessageType, 
	AgentCollaborationRequest,
	ExecutionContext,
	AgentResult
} from "../agents/types"
import { AgentRegistry } from "../agents/AgentRegistry"

/**
 * 消息路由规则
 */
interface MessageRoute {
	fromAgent?: string
	toAgent?: string
	messageType?: AgentMessageType
	handler: (message: AgentMessage, context: ExecutionContext) => Promise<void>
}

/**
 * 消息队列项
 */
interface QueuedMessage {
	message: AgentMessage
	context: ExecutionContext
	retryCount: number
	timestamp: Date
}

/**
 * 通信统计
 */
interface CommunicationStats {
	totalMessages: number
	messagesByType: Map<AgentMessageType, number>
	messagesByAgent: Map<string, number>
	failedMessages: number
	averageResponseTime: number
}

/**
 * 通信总线事件
 */
export interface CommunicationBusEvents {
	messageReceived: (message: AgentMessage) => void
	messageSent: (message: AgentMessage) => void
	messageDelivered: (messageId: string, agentId: string) => void
	messageFailure: (messageId: string, error: string) => void
	collaborationRequested: (request: AgentCollaborationRequest) => void
	collaborationCompleted: (requestId: string, result: AgentResult) => void
}

/**
 * Agent通信总线
 * 提供Agent间的可靠消息传递和协作机制
 */
export class AgentCommunicationBus extends EventEmitter {
	private agentRegistry: AgentRegistry
	private messageQueue: QueuedMessage[] = []
	private messageHistory: AgentMessage[] = []
	private messageRoutes: MessageRoute[] = []
	private collaborationRequests: Map<string, AgentCollaborationRequest> = new Map()
	private stats: CommunicationStats
	private processingInterval: NodeJS.Timeout | null = null
	private maxRetries: number = 3
	private messageTimeout: number = 30000 // 30秒

	constructor(agentRegistry: AgentRegistry) {
		super()
		this.agentRegistry = agentRegistry
		this.stats = {
			totalMessages: 0,
			messagesByType: new Map(),
			messagesByAgent: new Map(),
			failedMessages: 0,
			averageResponseTime: 0
		}
		this.startMessageProcessing()
	}

	/**
	 * 发送消息给特定Agent
	 */
	public async sendMessage(
		fromAgent: string, 
		toAgent: string, 
		messageType: AgentMessageType,
		content: any,
		context: ExecutionContext,
		relatedTaskId?: string
	): Promise<boolean> {
		const message: AgentMessage = {
			id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
			type: messageType,
			fromAgent,
			toAgent,
			content,
			timestamp: new Date(),
			relatedTaskId
		}

		return this.queueMessage(message, context)
	}

	/**
	 * 广播消息给所有Agent
	 */
	public async broadcast(
		fromAgent: string,
		messageType: AgentMessageType,
		content: any,
		context: ExecutionContext,
		filter?: (agent: Agent) => boolean
	): Promise<number> {
		const agents = this.agentRegistry.getAllAgentStatus()
		let sentCount = 0

		for (const { agent } of agents) {
			if (agent.id === fromAgent) {
				continue // 不发送给自己
			}

			if (filter && !filter(agent)) {
				continue // 不符合过滤条件
			}

			const success = await this.sendMessage(
				fromAgent,
				agent.id,
				messageType,
				content,
				context
			)

			if (success) {
				sentCount++
			}
		}

		return sentCount
	}

	/**
	 * 发送协作请求
	 */
	public async requestCollaboration(
		request: AgentCollaborationRequest,
		context: ExecutionContext
	): Promise<string> {
		this.collaborationRequests.set(request.requestId, request)
		this.emit("collaborationRequested", request)

		// 发送协作请求消息
		if (request.targetAgent) {
			// 发送给特定Agent
			await this.sendMessage(
				request.requestingAgent,
				request.targetAgent,
				AgentMessageType.REQUEST_COLLABORATION,
				request,
				context
			)
		} else {
			// 广播给所有能力匹配的Agent
			await this.broadcast(
				request.requestingAgent,
				AgentMessageType.REQUEST_COLLABORATION,
				request,
				context,
				(agent) => this.agentCanHandleRequest(agent, request)
			)
		}

		return request.requestId
	}

	/**
	 * 响应协作请求
	 */
	public async respondToCollaboration(
		requestId: string,
		respondingAgent: string,
		response: AgentResult,
		context: ExecutionContext
	): Promise<boolean> {
		const request = this.collaborationRequests.get(requestId)
		if (!request) {
			return false
		}

		// 发送响应消息
		const success = await this.sendMessage(
			respondingAgent,
			request.requestingAgent,
			AgentMessageType.PROVIDE_ASSISTANCE,
			{
				requestId,
				response
			},
			context,
			request.context.currentTask?.id
		)

		if (success) {
			this.emit("collaborationCompleted", requestId, response)
			this.collaborationRequests.delete(requestId)
		}

		return success
	}

	/**
	 * 添加消息路由规则
	 */
	public addMessageRoute(route: MessageRoute): void {
		this.messageRoutes.push(route)
	}

	/**
	 * 移除消息路由规则
	 */
	public removeMessageRoute(route: MessageRoute): boolean {
		const index = this.messageRoutes.indexOf(route)
		if (index >= 0) {
			this.messageRoutes.splice(index, 1)
			return true
		}
		return false
	}

	/**
	 * 同步Agent状态
	 */
	public async syncAgentStates(context: ExecutionContext): Promise<void> {
		const agents = this.agentRegistry.getAllAgentStatus()
		
		// 收集所有Agent状态
		const stateUpdate = {
			timestamp: new Date(),
			agents: agents.map(({ agent, registration }) => ({
				id: agent.id,
				name: agent.name,
				role: agent.role,
				status: agent.status,
				currentTasks: agent.getStatus().currentTasks.length,
				lastActivity: agent.getStatus().lastActivity
			}))
		}

		// 广播状态更新
		await this.broadcast(
			"system",
			AgentMessageType.STATUS_UPDATE,
			stateUpdate,
			context
		)
	}

	/**
	 * 将消息加入队列
	 */
	private async queueMessage(message: AgentMessage, context: ExecutionContext): Promise<boolean> {
		const queuedMessage: QueuedMessage = {
			message,
			context,
			retryCount: 0,
			timestamp: new Date()
		}

		this.messageQueue.push(queuedMessage)
		this.emit("messageReceived", message)
		
		// 更新统计
		this.updateStats(message)

		return true
	}

	/**
	 * 开始消息处理
	 */
	private startMessageProcessing(): void {
		this.processingInterval = setInterval(() => {
			this.processMessageQueue()
		}, 100) // 每100ms处理一次队列
	}

	/**
	 * 处理消息队列
	 */
	private async processMessageQueue(): Promise<void> {
		if (this.messageQueue.length === 0) {
			return
		}

		const messagesToProcess = this.messageQueue.splice(0, 10) // 每次处理最多10条消息
		
		for (const queuedMessage of messagesToProcess) {
			await this.processMessage(queuedMessage)
		}
	}

	/**
	 * 处理单个消息
	 */
	private async processMessage(queuedMessage: QueuedMessage): Promise<void> {
		const { message, context } = queuedMessage
		
		try {
			// 检查消息是否超时
			if (Date.now() - queuedMessage.timestamp.getTime() > this.messageTimeout) {
				this.emit("messageFailure", message.id, "Message timeout")
				return
			}

			// 应用路由规则
			const matchingRoutes = this.findMatchingRoutes(message)
			for (const route of matchingRoutes) {
				await route.handler(message, context)
			}

			// 如果有目标Agent，直接投递
			if (message.toAgent) {
				await this.deliverMessage(message, context)
			}

			this.emit("messageSent", message)
			this.messageHistory.push(message)

		} catch (error) {
			// 重试机制
			if (queuedMessage.retryCount < this.maxRetries) {
				queuedMessage.retryCount++
				this.messageQueue.push(queuedMessage)
			} else {
				this.stats.failedMessages++
				this.emit("messageFailure", message.id, error instanceof Error ? error.message : String(error))
			}
		}
	}

	/**
	 * 投递消息给目标Agent
	 */
	private async deliverMessage(message: AgentMessage, context: ExecutionContext): Promise<void> {
		if (!message.toAgent) {
			return
		}

		// 查找目标Agent
		const agentStatus = this.agentRegistry.getAllAgentStatus()
		const targetAgentInfo = agentStatus.find(({ agent }) => agent.id === message.toAgent)
		
		if (!targetAgentInfo) {
			throw new Error(`Target agent ${message.toAgent} not found`)
		}

		const targetAgent = targetAgentInfo.agent

		// 调用Agent的协作方法
		const result = await targetAgent.collaborate(message, context)
		
		if (result) {
			// 如果有响应，发送回复消息
			await this.sendMessage(
				message.toAgent!,
				message.fromAgent,
				AgentMessageType.PROVIDE_FEEDBACK,
				result,
				context,
				message.relatedTaskId
			)
		}

		this.emit("messageDelivered", message.id, message.toAgent!)
	}

	/**
	 * 查找匹配的路由规则
	 */
	private findMatchingRoutes(message: AgentMessage): MessageRoute[] {
		return this.messageRoutes.filter(route => {
			if (route.fromAgent && route.fromAgent !== message.fromAgent) {
				return false
			}
			if (route.toAgent && route.toAgent !== message.toAgent) {
				return false
			}
			if (route.messageType && route.messageType !== message.type) {
				return false
			}
			return true
		})
	}

	/**
	 * 检查Agent是否能处理协作请求
	 */
	private agentCanHandleRequest(agent: Agent, request: AgentCollaborationRequest): boolean {
		// 检查能力匹配
		const hasRequiredCapabilities = request.requiredCapabilities.some(
			capability => agent.capabilities.includes(capability)
		)
		
		// 检查Agent是否可用
		const isAvailable = agent.status === "idle" || agent.status === "busy"
		
		return hasRequiredCapabilities && isAvailable
	}

	/**
	 * 更新统计信息
	 */
	private updateStats(message: AgentMessage): void {
		this.stats.totalMessages++
		
		// 按类型统计
		const typeCount = this.stats.messagesByType.get(message.type) || 0
		this.stats.messagesByType.set(message.type, typeCount + 1)
		
		// 按Agent统计
		const agentCount = this.stats.messagesByAgent.get(message.fromAgent) || 0
		this.stats.messagesByAgent.set(message.fromAgent, agentCount + 1)
	}

	/**
	 * 获取通信统计
	 */
	public getStats(): CommunicationStats {
		return { ...this.stats }
	}

	/**
	 * 获取消息历史
	 */
	public getMessageHistory(limit?: number): AgentMessage[] {
		if (limit) {
			return this.messageHistory.slice(-limit)
		}
		return [...this.messageHistory]
	}

	/**
	 * 清理资源
	 */
	public cleanup(): void {
		if (this.processingInterval) {
			clearInterval(this.processingInterval)
			this.processingInterval = null
		}
		
		this.messageQueue = []
		this.messageHistory = []
		this.messageRoutes = []
		this.collaborationRequests.clear()
		this.removeAllListeners()
	}
}
