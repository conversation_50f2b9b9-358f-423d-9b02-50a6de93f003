/**
 * Agent系统导出文件
 * 统一导出所有Agent相关的类型、类和工具
 */

import { AgentFactory } from "./AgentFactory"
import { AgentRegistry } from "./AgentRegistry"

// 核心类型定义
export * from "./types"

// 基础Agent类
export { BaseAgent } from "./BaseAgent"

// Agent注册表
export { AgentRegistry, AgentSelectionStrategy } from "./AgentRegistry"

// Agent工厂
export { AgentFactory } from "./AgentFactory"

// 专业化Agent
export { CodeGenerationAgent, CodeGenerationTaskType } from "./specialized/CodeGenerationAgent"
export { CodeReviewAgent, CodeReviewTaskType } from "./specialized/CodeReviewAgent"
export { TestingAgent, TestingTaskType, TestType } from "./specialized/TestingAgent"

// 便利函数和工具
export class AgentSystem {
	private registry: AgentRegistry
	private factory: AgentFactory

	constructor() {
		this.registry = new AgentRegistry()
		this.factory = AgentFactory.getInstance()
	}

	/**
	 * 初始化Agent系统
	 */
	public async initialize(): Promise<void> {
		// 创建默认Agent集合
		const defaultAgents = this.factory.createDefaultAgentSet()
		
		// 注册所有Agent
		for (const agent of defaultAgents) {
			this.registry.registerAgent(agent)
		}
	}

	/**
	 * 获取Agent注册表
	 */
	public getRegistry(): AgentRegistry {
		return this.registry
	}

	/**
	 * 获取Agent工厂
	 */
	public getFactory(): AgentFactory {
		return this.factory
	}

	/**
	 * 清理资源
	 */
	public async cleanup(): Promise<void> {
		await this.registry.cleanup()
	}
}

// 默认导出Agent系统
export default AgentSystem
