/**
 * 基础Agent抽象类
 * 提供所有Agent的通用实现和基础功能
 */

import { v4 as uuidv4 } from "uuid"
import { 
	Agent, 
	AgentRole, 
	AgentCapability, 
	AgentStatus, 
	AgentConfig, 
	AgentTask, 
	AgentMessage, 
	AgentResult, 
	ExecutionContext,
	TaskStatus
} from "./types"

/**
 * 默认Agent配置
 */
const DEFAULT_AGENT_CONFIG: AgentConfig = {
	maxConcurrentTasks: 3,
	timeoutMs: 300000, // 5分钟
	retryAttempts: 3,
	autoApprovalEnabled: false,
	collaborationEnabled: true,
	preferredTools: []
}

/**
 * 基础Agent抽象类
 * 实现了Agent接口的通用功能，具体的执行逻辑由子类实现
 */
export abstract class BaseAgent implements Agent {
	public readonly id: string
	public readonly name: string
	public readonly role: AgentRole
	public readonly capabilities: AgentCapability[]
	public readonly version: string = "1.0.0"
	
	public status: AgentStatus = AgentStatus.IDLE
	public config: AgentConfig
	
	protected currentTasks: Map<string, AgentTask> = new Map()
	protected lastActivity: Date = new Date()
	protected executionContext?: ExecutionContext

	constructor(
		name: string,
		role: AgentRole,
		capabilities: AgentCapability[],
		config: Partial<AgentConfig> = {}
	) {
		this.id = uuidv4()
		this.name = name
		this.role = role
		this.capabilities = capabilities
		this.config = { ...DEFAULT_AGENT_CONFIG, ...config }
	}

	/**
	 * 判断Agent是否能够处理指定任务
	 * 基础实现检查能力匹配，子类可以重写以添加更复杂的逻辑
	 */
	public canHandle(task: AgentTask): boolean {
		// 检查状态
		if (this.status === AgentStatus.OFFLINE || this.status === AgentStatus.ERROR) {
			return false
		}
		
		// 检查并发任务限制
		if (this.currentTasks.size >= this.config.maxConcurrentTasks) {
			return false
		}
		
		// 检查能力匹配
		const hasRequiredCapabilities = task.requiredCapabilities.every(
			capability => this.capabilities.includes(capability)
		)
		
		if (!hasRequiredCapabilities) {
			return false
		}
		
		// 子类可以重写此方法添加更多检查
		return this.canHandleSpecific(task)
	}

	/**
	 * 子类特定的任务处理能力检查
	 * 子类可以重写此方法实现特定的逻辑
	 */
	protected canHandleSpecific(task: AgentTask): boolean {
		return true
	}

	/**
	 * 执行任务的主要方法
	 * 包含通用的执行流程，具体执行逻辑由子类实现
	 */
	public async execute(task: AgentTask, context: ExecutionContext): Promise<AgentResult> {
		this.lastActivity = new Date()
		
		try {
			// 检查是否能处理任务
			if (!this.canHandle(task)) {
				return {
					success: false,
					error: `Agent ${this.name} cannot handle task ${task.id}`
				}
			}

			// 更新任务状态
			task.status = TaskStatus.IN_PROGRESS
			task.updatedAt = new Date()
			this.currentTasks.set(task.id, task)
			this.status = AgentStatus.BUSY
			this.executionContext = context

			// 执行具体任务逻辑
			const result = await this.executeTask(task, context)
			
			// 更新任务状态
			if (result.success) {
				task.status = TaskStatus.COMPLETED
			} else {
				task.status = TaskStatus.FAILED
			}
			task.updatedAt = new Date()
			
			// 清理任务
			this.currentTasks.delete(task.id)
			if (this.currentTasks.size === 0) {
				this.status = AgentStatus.IDLE
			}

			return result

		} catch (error) {
			// 错误处理
			task.status = TaskStatus.FAILED
			task.updatedAt = new Date()
			this.currentTasks.delete(task.id)
			this.status = this.currentTasks.size > 0 ? AgentStatus.BUSY : AgentStatus.IDLE

			return {
				success: false,
				error: error instanceof Error ? error.message : String(error)
			}
		}
	}

	/**
	 * 子类必须实现的具体任务执行方法
	 */
	protected abstract executeTask(task: AgentTask, context: ExecutionContext): Promise<AgentResult>

	/**
	 * 处理来自其他Agent的协作消息
	 * 基础实现提供通用的消息处理框架
	 */
	public async collaborate(message: AgentMessage, context: ExecutionContext): Promise<AgentResult | null> {
		if (!this.config.collaborationEnabled) {
			return null
		}

		this.lastActivity = new Date()
		
		try {
			return await this.handleCollaborationMessage(message, context)
		} catch (error) {
			return {
				success: false,
				error: `Collaboration failed: ${error instanceof Error ? error.message : String(error)}`
			}
		}
	}

	/**
	 * 子类可以重写的协作消息处理方法
	 */
	protected async handleCollaborationMessage(
		message: AgentMessage, 
		context: ExecutionContext
	): Promise<AgentResult | null> {
		// 默认实现：记录消息但不执行任何操作
		console.log(`Agent ${this.name} received collaboration message from ${message.fromAgent}`)
		return null
	}

	/**
	 * 初始化Agent
	 */
	public async initialize(context: ExecutionContext): Promise<void> {
		this.executionContext = context
		this.status = AgentStatus.IDLE
		this.lastActivity = new Date()
		
		// 子类可以重写此方法添加特定的初始化逻辑
		await this.initializeSpecific(context)
	}

	/**
	 * 子类特定的初始化逻辑
	 */
	protected async initializeSpecific(context: ExecutionContext): Promise<void> {
		// 默认空实现
	}

	/**
	 * 清理资源
	 */
	public async cleanup(): Promise<void> {
		this.status = AgentStatus.OFFLINE
		this.currentTasks.clear()
		
		// 子类可以重写此方法添加特定的清理逻辑
		await this.cleanupSpecific()
	}

	/**
	 * 子类特定的清理逻辑
	 */
	protected async cleanupSpecific(): Promise<void> {
		// 默认空实现
	}

	/**
	 * 获取Agent当前状态信息
	 */
	public getStatus() {
		return {
			status: this.status,
			currentTasks: Array.from(this.currentTasks.values()),
			capabilities: this.capabilities,
			lastActivity: this.lastActivity
		}
	}

	/**
	 * 更新Agent配置
	 */
	public updateConfig(config: Partial<AgentConfig>): void {
		this.config = { ...this.config, ...config }
	}

	/**
	 * 检查Agent是否可用
	 */
	public isAvailable(): boolean {
		return this.status === AgentStatus.IDLE && this.currentTasks.size < this.config.maxConcurrentTasks
	}

	/**
	 * 获取Agent的负载情况
	 */
	public getLoad(): number {
		return this.currentTasks.size / this.config.maxConcurrentTasks
	}

	/**
	 * 创建标准的成功结果
	 */
	protected createSuccessResult(data?: any, toolResponses?: any[]): AgentResult {
		return {
			success: true,
			data,
			toolResponses
		}
	}

	/**
	 * 创建标准的错误结果
	 */
	protected createErrorResult(error: string): AgentResult {
		return {
			success: false,
			error
		}
	}
}
