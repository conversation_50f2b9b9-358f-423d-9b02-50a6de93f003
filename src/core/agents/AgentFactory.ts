/**
 * Agent工厂
 * 负责创建和配置各种专业化Agent
 */

import { Agent, Agent<PERSON><PERSON>, AgentConfig, AgentFactory as IAgentFactory } from "./types"
import { CodeGenerationAgent } from "./specialized/CodeGenerationAgent"
import { CodeReviewAgent } from "./specialized/CodeReviewAgent"
import { TestingAgent } from "./specialized/TestingAgent"

/**
 * Agent工厂实现
 * 提供统一的Agent创建接口
 */
export class AgentFactory implements IAgentFactory {
	private static instance: AgentFactory | null = null

	/**
	 * 获取工厂单例
	 */
	public static getInstance(): AgentFactory {
		if (!AgentFactory.instance) {
			AgentFactory.instance = new AgentFactory()
		}
		return AgentFactory.instance
	}

	/**
	 * 创建Agent实例
	 */
	public createAgent(role: AgentRole, config?: Partial<AgentConfig>): Agent {
		switch (role) {
			case AgentRole.CODE_GENERATION:
				return this.createCodeGenerationAgent(config)
			
			case AgentRole.CODE_REVIEW:
				return this.createCodeReviewAgent(config)
			
			case AgentRole.TESTING:
				return this.createTestingAgent(config)
			
			case AgentRole.DEBUGGING:
				return this.createDebuggingAgent(config)
			
			case AgentRole.DOCUMENTATION:
				return this.createDocumentationAgent(config)
			
			case AgentRole.REFACTORING:
				return this.createRefactoringAgent(config)
			
			case AgentRole.SECURITY_ANALYSIS:
				return this.createSecurityAnalysisAgent(config)
			
			case AgentRole.PERFORMANCE_OPTIMIZATION:
				return this.createPerformanceOptimizationAgent(config)
			
			case AgentRole.PROJECT_MANAGEMENT:
				return this.createProjectManagementAgent(config)
			
			case AgentRole.GENERAL_ASSISTANT:
				return this.createGeneralAssistantAgent(config)
			
			default:
				throw new Error(`Unsupported agent role: ${role}`)
		}
	}

	/**
	 * 获取支持的Agent角色
	 */
	public getSupportedRoles(): AgentRole[] {
		return Object.values(AgentRole)
	}

	/**
	 * 批量创建Agent
	 */
	public createAgents(roles: AgentRole[], config?: Partial<AgentConfig>): Agent[] {
		return roles.map(role => this.createAgent(role, config))
	}

	/**
	 * 创建默认Agent集合
	 * 包含最常用的Agent类型
	 */
	public createDefaultAgentSet(config?: Partial<AgentConfig>): Agent[] {
		const defaultRoles = [
			AgentRole.CODE_GENERATION,
			AgentRole.CODE_REVIEW,
			AgentRole.TESTING,
			AgentRole.GENERAL_ASSISTANT
		]
		
		return this.createAgents(defaultRoles, config)
	}

	/**
	 * 创建完整Agent集合
	 * 包含所有可用的Agent类型
	 */
	public createFullAgentSet(config?: Partial<AgentConfig>): Agent[] {
		return this.createAgents(this.getSupportedRoles(), config)
	}

	/**
	 * 创建代码生成Agent
	 */
	private createCodeGenerationAgent(config?: Partial<AgentConfig>): Agent {
		const agent = new CodeGenerationAgent()
		if (config) {
			agent.updateConfig(config)
		}
		return agent
	}

	/**
	 * 创建代码审查Agent
	 */
	private createCodeReviewAgent(config?: Partial<AgentConfig>): Agent {
		const agent = new CodeReviewAgent()
		if (config) {
			agent.updateConfig(config)
		}
		return agent
	}

	/**
	 * 创建测试Agent
	 */
	private createTestingAgent(config?: Partial<AgentConfig>): Agent {
		const agent = new TestingAgent()
		if (config) {
			agent.updateConfig(config)
		}
		return agent
	}

	/**
	 * 创建调试Agent
	 * TODO: 实现DebuggingAgent类
	 */
	private createDebuggingAgent(config?: Partial<AgentConfig>): Agent {
		// 暂时返回通用Agent，后续实现专门的DebuggingAgent
		throw new Error("DebuggingAgent not yet implemented")
	}

	/**
	 * 创建文档Agent
	 * TODO: 实现DocumentationAgent类
	 */
	private createDocumentationAgent(config?: Partial<AgentConfig>): Agent {
		// 暂时返回通用Agent，后续实现专门的DocumentationAgent
		throw new Error("DocumentationAgent not yet implemented")
	}

	/**
	 * 创建重构Agent
	 * TODO: 实现RefactoringAgent类
	 */
	private createRefactoringAgent(config?: Partial<AgentConfig>): Agent {
		// 暂时返回通用Agent，后续实现专门的RefactoringAgent
		throw new Error("RefactoringAgent not yet implemented")
	}

	/**
	 * 创建安全分析Agent
	 * TODO: 实现SecurityAnalysisAgent类
	 */
	private createSecurityAnalysisAgent(config?: Partial<AgentConfig>): Agent {
		// 暂时返回通用Agent，后续实现专门的SecurityAnalysisAgent
		throw new Error("SecurityAnalysisAgent not yet implemented")
	}

	/**
	 * 创建性能优化Agent
	 * TODO: 实现PerformanceOptimizationAgent类
	 */
	private createPerformanceOptimizationAgent(config?: Partial<AgentConfig>): Agent {
		// 暂时返回通用Agent，后续实现专门的PerformanceOptimizationAgent
		throw new Error("PerformanceOptimizationAgent not yet implemented")
	}

	/**
	 * 创建项目管理Agent
	 * TODO: 实现ProjectManagementAgent类
	 */
	private createProjectManagementAgent(config?: Partial<AgentConfig>): Agent {
		// 暂时返回通用Agent，后续实现专门的ProjectManagementAgent
		throw new Error("ProjectManagementAgent not yet implemented")
	}

	/**
	 * 创建通用助手Agent
	 * TODO: 实现GeneralAssistantAgent类
	 */
	private createGeneralAssistantAgent(config?: Partial<AgentConfig>): Agent {
		// 暂时返回通用Agent，后续实现专门的GeneralAssistantAgent
		throw new Error("GeneralAssistantAgent not yet implemented")
	}

	/**
	 * 验证Agent配置
	 */
	private validateAgentConfig(config: AgentConfig): boolean {
		return (
			config.maxConcurrentTasks > 0 &&
			config.timeoutMs > 0 &&
			config.retryAttempts >= 0
		)
	}

	/**
	 * 获取Agent的默认配置
	 */
	public getDefaultConfig(role: AgentRole): AgentConfig {
		const baseConfig: AgentConfig = {
			maxConcurrentTasks: 3,
			timeoutMs: 300000, // 5分钟
			retryAttempts: 3,
			autoApprovalEnabled: false,
			collaborationEnabled: true,
			preferredTools: []
		}

		// 根据角色调整配置
		switch (role) {
			case AgentRole.CODE_GENERATION:
				return {
					...baseConfig,
					maxConcurrentTasks: 2, // 代码生成通常需要更多资源
					timeoutMs: 600000, // 10分钟
					preferredTools: ["write_to_file", "str_replace_editor"]
				}
			
			case AgentRole.CODE_REVIEW:
				return {
					...baseConfig,
					maxConcurrentTasks: 5, // 代码审查可以并发处理更多任务
					timeoutMs: 180000, // 3分钟
					preferredTools: ["read_file", "list_files"]
				}
			
			case AgentRole.TESTING:
				return {
					...baseConfig,
					maxConcurrentTasks: 3,
					timeoutMs: 900000, // 15分钟，测试可能需要更长时间
					preferredTools: ["execute_command", "write_to_file"]
				}
			
			default:
				return baseConfig
		}
	}

	/**
	 * 创建带有角色特定配置的Agent
	 */
	public createAgentWithRoleConfig(role: AgentRole, customConfig?: Partial<AgentConfig>): Agent {
		const defaultConfig = this.getDefaultConfig(role)
		const finalConfig = { ...defaultConfig, ...customConfig }
		
		if (!this.validateAgentConfig(finalConfig)) {
			throw new Error(`Invalid agent configuration for role: ${role}`)
		}
		
		return this.createAgent(role, finalConfig)
	}

	/**
	 * 获取Agent能力映射
	 */
	public getAgentCapabilities(): Record<AgentRole, string[]> {
		return {
			[AgentRole.CODE_GENERATION]: ["code-writing", "file-creation", "file-editing", "refactoring"],
			[AgentRole.CODE_REVIEW]: ["code-analysis", "bug-detection", "style-checking"],
			[AgentRole.TESTING]: ["test-generation", "test-execution", "coverage-analysis"],
			[AgentRole.DEBUGGING]: ["debugging", "error-analysis"],
			[AgentRole.DOCUMENTATION]: ["documentation-generation", "comment-generation"],
			[AgentRole.REFACTORING]: ["refactoring", "code-optimization"],
			[AgentRole.SECURITY_ANALYSIS]: ["security-scanning", "vulnerability-detection"],
			[AgentRole.PERFORMANCE_OPTIMIZATION]: ["performance-analysis", "optimization"],
			[AgentRole.PROJECT_MANAGEMENT]: ["project-planning", "task-coordination"],
			[AgentRole.GENERAL_ASSISTANT]: ["terminal-operations", "browser-automation", "mcp-tool-usage"]
		}
	}

	/**
	 * 根据能力需求推荐Agent
	 */
	public recommendAgentsForCapabilities(requiredCapabilities: string[]): AgentRole[] {
		const capabilities = this.getAgentCapabilities()
		const recommendations: AgentRole[] = []
		
		for (const [role, agentCapabilities] of Object.entries(capabilities)) {
			const hasRequiredCapability = requiredCapabilities.some(
				capability => agentCapabilities.includes(capability)
			)
			
			if (hasRequiredCapability) {
				recommendations.push(role as AgentRole)
			}
		}
		
		return recommendations
	}
}
