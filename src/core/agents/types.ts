/**
 * 多智能体系统的核心类型定义
 * 定义了Agent接口、角色、任务和执行上下文等基础类型
 */

import { Anthropic } from "@anthropic-ai/sdk"
import { ToolResponse } from "../task"

/**
 * Agent角色枚举
 * 定义了不同专业化Agent的角色类型
 */
export enum AgentRole {
	CODE_GENERATION = "code_generation",
	CODE_REVIEW = "code_review", 
	TESTING = "testing",
	DEBUGGING = "debugging",
	DOCUMENTATION = "documentation",
	REFACTORING = "refactoring",
	SECURITY_ANALYSIS = "security_analysis",
	PERFORMANCE_OPTIMIZATION = "performance_optimization",
	PROJECT_MANAGEMENT = "project_management",
	GENERAL_ASSISTANT = "general_assistant"
}

/**
 * Agent能力标识
 * 定义了Agent可以执行的具体能力
 */
export type AgentCapability = 
	| "code-writing"
	| "file-creation" 
	| "file-editing"
	| "refactoring"
	| "code-analysis"
	| "bug-detection"
	| "style-checking"
	| "test-generation"
	| "test-execution"
	| "coverage-analysis"
	| "debugging"
	| "performance-analysis"
	| "security-scanning"
	| "documentation-generation"
	| "project-planning"
	| "task-coordination"
	| "terminal-operations"
	| "browser-automation"
	| "mcp-tool-usage"

/**
 * 任务优先级
 */
export enum TaskPriority {
	LOW = "low",
	MEDIUM = "medium", 
	HIGH = "high",
	CRITICAL = "critical"
}

/**
 * 任务状态
 */
export enum TaskStatus {
	PENDING = "pending",
	IN_PROGRESS = "in_progress",
	COMPLETED = "completed",
	FAILED = "failed",
	CANCELLED = "cancelled"
}

/**
 * Agent任务定义
 * 描述了Agent需要执行的具体任务
 */
export interface AgentTask {
	id: string
	type: string
	description: string
	priority: TaskPriority
	status: TaskStatus
	requiredCapabilities: AgentCapability[]
	parameters: Record<string, any>
	dependencies?: string[] // 依赖的其他任务ID
	parentTaskId?: string
	createdAt: Date
	updatedAt: Date
	estimatedDuration?: number // 预估执行时间（分钟）
}

/**
 * Agent执行上下文
 * 包含Agent执行任务时需要的所有上下文信息
 */
export interface ExecutionContext {
	// 项目上下文
	workspaceRoot: string
	currentWorkingDirectory: string
	projectFiles: string[]
	
	// 对话上下文
	conversationHistory: Anthropic.Messages.MessageParam[]
	currentUserMessage?: string
	
	// 任务上下文
	currentTask: AgentTask
	relatedTasks: AgentTask[]
	sharedState: Record<string, any>
	
	// 工具和服务
	availableTools: string[]
	mcpServers: string[]
	
	// 执行环境
	timeoutMs?: number
	maxRetries?: number
	
	// 协作上下文
	collaboratingAgents: string[]
	messageHistory: AgentMessage[]
}

/**
 * Agent执行结果
 */
export interface AgentResult {
	success: boolean
	data?: any
	error?: string
	toolResponses?: ToolResponse[]
	nextActions?: AgentTask[]
	collaborationRequests?: AgentCollaborationRequest[]
	updatedContext?: Partial<ExecutionContext>
}

/**
 * Agent间消息类型
 */
export enum AgentMessageType {
	REQUEST_COLLABORATION = "request_collaboration",
	PROVIDE_ASSISTANCE = "provide_assistance", 
	SHARE_RESULT = "share_result",
	REQUEST_REVIEW = "request_review",
	PROVIDE_FEEDBACK = "provide_feedback",
	STATUS_UPDATE = "status_update",
	ERROR_NOTIFICATION = "error_notification"
}

/**
 * Agent间消息
 */
export interface AgentMessage {
	id: string
	type: AgentMessageType
	fromAgent: string
	toAgent?: string // undefined表示广播消息
	content: any
	timestamp: Date
	relatedTaskId?: string
}

/**
 * Agent协作请求
 */
export interface AgentCollaborationRequest {
	requestId: string
	requestingAgent: string
	targetAgent?: string // undefined表示向所有Agent请求
	requestType: AgentMessageType
	description: string
	requiredCapabilities: AgentCapability[]
	context: Partial<ExecutionContext>
	priority: TaskPriority
	timeoutMs?: number
}

/**
 * Agent状态
 */
export enum AgentStatus {
	IDLE = "idle",
	BUSY = "busy", 
	ERROR = "error",
	OFFLINE = "offline"
}

/**
 * Agent配置
 */
export interface AgentConfig {
	maxConcurrentTasks: number
	timeoutMs: number
	retryAttempts: number
	autoApprovalEnabled: boolean
	collaborationEnabled: boolean
	preferredTools: string[]
}

/**
 * 核心Agent接口
 * 定义了所有Agent必须实现的基础方法
 */
export interface Agent {
	// 基础属性
	readonly id: string
	readonly name: string
	readonly role: AgentRole
	readonly capabilities: AgentCapability[]
	readonly version: string
	
	// 状态管理
	status: AgentStatus
	config: AgentConfig
	
	// 核心方法
	/**
	 * 判断Agent是否能够处理指定任务
	 */
	canHandle(task: AgentTask): boolean
	
	/**
	 * 执行任务
	 */
	execute(task: AgentTask, context: ExecutionContext): Promise<AgentResult>
	
	/**
	 * 处理来自其他Agent的协作消息
	 */
	collaborate(message: AgentMessage, context: ExecutionContext): Promise<AgentResult | null>
	
	/**
	 * 初始化Agent
	 */
	initialize(context: ExecutionContext): Promise<void>
	
	/**
	 * 清理资源
	 */
	cleanup(): Promise<void>
	
	/**
	 * 获取Agent当前状态信息
	 */
	getStatus(): {
		status: AgentStatus
		currentTasks: AgentTask[]
		capabilities: AgentCapability[]
		lastActivity: Date
	}
	
	/**
	 * 更新Agent配置
	 */
	updateConfig(config: Partial<AgentConfig>): void
}

/**
 * Agent工厂接口
 * 用于创建不同类型的Agent实例
 */
export interface AgentFactory {
	createAgent(role: AgentRole, config?: Partial<AgentConfig>): Agent
	getSupportedRoles(): AgentRole[]
}
