/**
 * Agent注册和管理系统
 * 负责Agent的注册、发现、生命周期管理和负载均衡
 */

import { EventEmitter } from "events"
import { 
	Agent, 
	AgentRole, 
	AgentCapability, 
	AgentTask, 
	AgentStatus, 
	ExecutionContext,
	TaskPriority 
} from "./types"

/**
 * Agent注册信息
 */
interface AgentRegistration {
	agent: Agent
	registeredAt: Date
	lastHealthCheck: Date
	isHealthy: boolean
	metrics: AgentMetrics
}

/**
 * Agent性能指标
 */
interface AgentMetrics {
	tasksCompleted: number
	tasksFailedCount: number
	averageExecutionTime: number
	successRate: number
	lastExecutionTime?: number
}

/**
 * Agent选择策略
 */
export enum AgentSelectionStrategy {
	ROUND_ROBIN = "round_robin",
	LEAST_LOADED = "least_loaded", 
	BEST_PERFORMANCE = "best_performance",
	CAPABILITY_MATCH = "capability_match",
	PRIORITY_BASED = "priority_based"
}

/**
 * Agent注册表事件
 */
export interface AgentRegistryEvents {
	agentRegistered: (agent: Agent) => void
	agentUnregistered: (agentId: string) => void
	agentStatusChanged: (agentId: string, status: AgentStatus) => void
	agentHealthCheckFailed: (agentId: string, error: Error) => void
}

/**
 * Agent注册表
 * 管理所有Agent的注册、发现和选择
 */
export class AgentRegistry extends EventEmitter {
	private agents: Map<string, AgentRegistration> = new Map()
	private roleIndex: Map<AgentRole, Set<string>> = new Map()
	private capabilityIndex: Map<AgentCapability, Set<string>> = new Map()
	private healthCheckInterval: NodeJS.Timeout | null = null
	private selectionStrategy: AgentSelectionStrategy = AgentSelectionStrategy.LEAST_LOADED

	constructor(
		private healthCheckIntervalMs: number = 30000, // 30秒
		selectionStrategy: AgentSelectionStrategy = AgentSelectionStrategy.LEAST_LOADED
	) {
		super()
		this.selectionStrategy = selectionStrategy
		this.startHealthChecks()
	}

	/**
	 * 注册Agent
	 */
	public registerAgent(agent: Agent): void {
		if (this.agents.has(agent.id)) {
			throw new Error(`Agent with id ${agent.id} is already registered`)
		}

		const registration: AgentRegistration = {
			agent,
			registeredAt: new Date(),
			lastHealthCheck: new Date(),
			isHealthy: true,
			metrics: {
				tasksCompleted: 0,
				tasksFailedCount: 0,
				averageExecutionTime: 0,
				successRate: 1.0
			}
		}

		// 添加到主索引
		this.agents.set(agent.id, registration)

		// 添加到角色索引
		if (!this.roleIndex.has(agent.role)) {
			this.roleIndex.set(agent.role, new Set())
		}
		this.roleIndex.get(agent.role)!.add(agent.id)

		// 添加到能力索引
		agent.capabilities.forEach(capability => {
			if (!this.capabilityIndex.has(capability)) {
				this.capabilityIndex.set(capability, new Set())
			}
			this.capabilityIndex.get(capability)!.add(agent.id)
		})

		this.emit("agentRegistered", agent)
	}

	/**
	 * 注销Agent
	 */
	public unregisterAgent(agentId: string): boolean {
		const registration = this.agents.get(agentId)
		if (!registration) {
			return false
		}

		const agent = registration.agent

		// 从主索引移除
		this.agents.delete(agentId)

		// 从角色索引移除
		const roleAgents = this.roleIndex.get(agent.role)
		if (roleAgents) {
			roleAgents.delete(agentId)
			if (roleAgents.size === 0) {
				this.roleIndex.delete(agent.role)
			}
		}

		// 从能力索引移除
		agent.capabilities.forEach(capability => {
			const capabilityAgents = this.capabilityIndex.get(capability)
			if (capabilityAgents) {
				capabilityAgents.delete(agentId)
				if (capabilityAgents.size === 0) {
					this.capabilityIndex.delete(capability)
				}
			}
		})

		// 清理Agent资源
		agent.cleanup().catch(error => {
			console.error(`Error cleaning up agent ${agentId}:`, error)
		})

		this.emit("agentUnregistered", agentId)
		return true
	}

	/**
	 * 根据角色查找Agent
	 */
	public getAgentsByRole(role: AgentRole): Agent[] {
		const agentIds = this.roleIndex.get(role) || new Set()
		return Array.from(agentIds)
			.map(id => this.agents.get(id)?.agent)
			.filter((agent): agent is Agent => agent !== undefined && this.isAgentHealthy(agent.id))
	}

	/**
	 * 根据能力查找Agent
	 */
	public getAgentsByCapability(capability: AgentCapability): Agent[] {
		const agentIds = this.capabilityIndex.get(capability) || new Set()
		return Array.from(agentIds)
			.map(id => this.agents.get(id)?.agent)
			.filter((agent): agent is Agent => agent !== undefined && this.isAgentHealthy(agent.id))
	}

	/**
	 * 为任务选择最适合的Agent
	 */
	public selectAgentForTask(task: AgentTask): Agent | null {
		// 首先筛选能够处理任务的Agent
		const candidateAgents = this.findCandidateAgents(task)
		
		if (candidateAgents.length === 0) {
			return null
		}

		// 根据选择策略选择最佳Agent
		return this.applySelectionStrategy(candidateAgents, task)
	}

	/**
	 * 查找能够处理任务的候选Agent
	 */
	private findCandidateAgents(task: AgentTask): Agent[] {
		const candidates: Agent[] = []

		// 遍历所有健康的Agent
		for (const [agentId, registration] of this.agents) {
			if (!this.isAgentHealthy(agentId)) {
				continue
			}

			const agent = registration.agent
			if (agent.canHandle(task)) {
				candidates.push(agent)
			}
		}

		return candidates
	}

	/**
	 * 应用选择策略
	 */
	private applySelectionStrategy(candidates: Agent[], task: AgentTask): Agent | null {
		if (candidates.length === 0) {
			return null
		}

		switch (this.selectionStrategy) {
			case AgentSelectionStrategy.ROUND_ROBIN:
				return this.selectRoundRobin(candidates)
			
			case AgentSelectionStrategy.LEAST_LOADED:
				return this.selectLeastLoaded(candidates)
			
			case AgentSelectionStrategy.BEST_PERFORMANCE:
				return this.selectBestPerformance(candidates)
			
			case AgentSelectionStrategy.CAPABILITY_MATCH:
				return this.selectBestCapabilityMatch(candidates, task)
			
			case AgentSelectionStrategy.PRIORITY_BASED:
				return this.selectByPriority(candidates, task)
			
			default:
				return candidates[0]
		}
	}

	/**
	 * 轮询选择
	 */
	private selectRoundRobin(candidates: Agent[]): Agent {
		// 简单实现：选择注册时间最早的可用Agent
		return candidates.sort((a, b) => {
			const regA = this.agents.get(a.id)!.registeredAt
			const regB = this.agents.get(b.id)!.registeredAt
			return regA.getTime() - regB.getTime()
		})[0]
	}

	/**
	 * 选择负载最低的Agent
	 */
	private selectLeastLoaded(candidates: Agent[]): Agent {
		return candidates.reduce((best, current) => {
			const bestLoad = best.getStatus().currentTasks.length
			const currentLoad = current.getStatus().currentTasks.length
			return currentLoad < bestLoad ? current : best
		})
	}

	/**
	 * 选择性能最佳的Agent
	 */
	private selectBestPerformance(candidates: Agent[]): Agent {
		return candidates.reduce((best, current) => {
			const bestMetrics = this.agents.get(best.id)!.metrics
			const currentMetrics = this.agents.get(current.id)!.metrics
			
			// 综合考虑成功率和平均执行时间
			const bestScore = bestMetrics.successRate / (bestMetrics.averageExecutionTime || 1)
			const currentScore = currentMetrics.successRate / (currentMetrics.averageExecutionTime || 1)
			
			return currentScore > bestScore ? current : best
		})
	}

	/**
	 * 选择能力匹配度最高的Agent
	 */
	private selectBestCapabilityMatch(candidates: Agent[], task: AgentTask): Agent {
		return candidates.reduce((best, current) => {
			const bestMatch = this.calculateCapabilityMatch(best, task)
			const currentMatch = this.calculateCapabilityMatch(current, task)
			return currentMatch > bestMatch ? current : best
		})
	}

	/**
	 * 根据任务优先级选择Agent
	 */
	private selectByPriority(candidates: Agent[], task: AgentTask): Agent {
		// 高优先级任务优先选择性能最佳的Agent
		if (task.priority === TaskPriority.HIGH || task.priority === TaskPriority.CRITICAL) {
			return this.selectBestPerformance(candidates)
		}
		// 低优先级任务选择负载最低的Agent
		return this.selectLeastLoaded(candidates)
	}

	/**
	 * 计算Agent与任务的能力匹配度
	 */
	private calculateCapabilityMatch(agent: Agent, task: AgentTask): number {
		const requiredCapabilities = new Set(task.requiredCapabilities)
		const agentCapabilities = new Set(agent.capabilities)
		
		// 计算交集
		const intersection = new Set([...requiredCapabilities].filter(x => agentCapabilities.has(x)))
		
		// 匹配度 = 交集大小 / 需求能力数量
		return intersection.size / requiredCapabilities.size
	}

	/**
	 * 检查Agent是否健康
	 */
	private isAgentHealthy(agentId: string): boolean {
		const registration = this.agents.get(agentId)
		return registration?.isHealthy === true && registration.agent.status !== AgentStatus.OFFLINE
	}

	/**
	 * 更新Agent指标
	 */
	public updateAgentMetrics(agentId: string, executionTime: number, success: boolean): void {
		const registration = this.agents.get(agentId)
		if (!registration) {
			return
		}

		const metrics = registration.metrics
		
		if (success) {
			metrics.tasksCompleted++
		} else {
			metrics.tasksFailedCount++
		}

		// 更新平均执行时间
		const totalTasks = metrics.tasksCompleted + metrics.tasksFailedCount
		metrics.averageExecutionTime = 
			(metrics.averageExecutionTime * (totalTasks - 1) + executionTime) / totalTasks

		// 更新成功率
		metrics.successRate = metrics.tasksCompleted / totalTasks
		metrics.lastExecutionTime = executionTime
	}

	/**
	 * 获取所有Agent的状态
	 */
	public getAllAgentStatus(): Array<{agent: Agent, registration: AgentRegistration}> {
		return Array.from(this.agents.entries()).map(([id, registration]) => ({
			agent: registration.agent,
			registration
		}))
	}

	/**
	 * 设置选择策略
	 */
	public setSelectionStrategy(strategy: AgentSelectionStrategy): void {
		this.selectionStrategy = strategy
	}

	/**
	 * 开始健康检查
	 */
	private startHealthChecks(): void {
		this.healthCheckInterval = setInterval(() => {
			this.performHealthChecks()
		}, this.healthCheckIntervalMs)
	}

	/**
	 * 执行健康检查
	 */
	private async performHealthChecks(): Promise<void> {
		for (const [agentId, registration] of this.agents) {
			try {
				const status = registration.agent.getStatus()
				const wasHealthy = registration.isHealthy
				registration.isHealthy = status.status !== AgentStatus.ERROR
				registration.lastHealthCheck = new Date()

				if (wasHealthy !== registration.isHealthy) {
					this.emit("agentStatusChanged", agentId, status.status)
				}
			} catch (error) {
				registration.isHealthy = false
				this.emit("agentHealthCheckFailed", agentId, error as Error)
			}
		}
	}

	/**
	 * 停止健康检查
	 */
	public stopHealthChecks(): void {
		if (this.healthCheckInterval) {
			clearInterval(this.healthCheckInterval)
			this.healthCheckInterval = null
		}
	}

	/**
	 * 清理资源
	 */
	public async cleanup(): Promise<void> {
		this.stopHealthChecks()
		
		// 注销所有Agent
		const agentIds = Array.from(this.agents.keys())
		await Promise.all(agentIds.map(id => this.unregisterAgent(id)))
		
		this.removeAllListeners()
	}
}
