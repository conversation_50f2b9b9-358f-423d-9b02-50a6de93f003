syntax = "proto3";

package host;
option java_package = "bot.cline.host.proto";
option java_multiple_files = true;

import "common.proto";

// Provides methods for working with the user's environment.
service EnvService {
  // Writes text to the system clipboard.
  rpc clipboardWriteText(cline.StringRequest) returns (cline.Empty);

  // Reads text from the system clipboard.
  rpc clipboardReadText(cline.EmptyRequest) returns (cline.String);

  // Opens a URL in the user's default browser or application.
  rpc openExternal(cline.StringRequest) returns (cline.Empty);
}
